from dataclasses import dataclass


@dataclass(frozen=True)
class DataServicePrefixes:
    V3 = "/v3"

    AGGREGATION = "/aggs"
    ANALYZE = "/analyze"
    AI = "/ai"
    ASSET = "/asset"
    DOCUMENT = "/document"
    RECORD = "/record"
    EVENT = "/event"
    PLAN_PREFIX = "/plan"
    USE_CASE_PREFIX = "/use_case"
    TEMPLATE_PREFIX = "/template"
    CONTACT = "/contact"
    PLACE = "/place"
    EXPERIMENTAL_PREFIX = "/x"
    LOOKUP_PREFIX = "/lookup"  # @REVIEW: What ought to be the name for this?


@dataclass(frozen=True)
class AssetEndpointRoutes:
    BY_ID = "/by_id/"
    URL = "/url/"


@dataclass(frozen=True)
class RecordEndpointRoutes:
    BASE = "/"


@dataclass(frozen=True)
class EventEndpointRoutes:
    BASE = "/"
    FEED = "/feed/"
    MODIFY_ASSETS = "/modify_assets/"
    BY_ID = "/by_id/"
    GROUP_REORDER = "/group/reorder/"


@dataclass(frozen=True)
class AggregationEndpointRoutes:
    BASE = "/"
    FREQUENCY_DISTRIBUTION = "/frequency_distribution/"
    DATE_HISTOGRAM = "/date_histogram/"
    CALENDAR_FREQUENCY_DISTRIBUTION = "/calendar_frequency_distribution/"
    CALENDAR_HISTOGRAM_AGGREGATION = "/calendar_histogram_aggregation/"


@dataclass(frozen=True)
class AnalyzeEndpointRoutes:
    BASE = "/"
    TREND_DETECT = "/trend_detect/"
    CORRELATE_EVENT = "/correlate/event/"
    CORRELATE_EVENT_SUGGEST_PARAMETERS = "/correlate/event/suggest/parameters"
    ANALYSE_DATA_SERIES = "/analyse_data_series/"


@dataclass(frozen=True)
class AIEndpointRoutes:
    BASE = "/"
    SUGGEST_EVENT = "/suggest/event/"
    CLASSIFY_EVENT_CATEGORY = "/classify/event/"


@dataclass(frozen=True)
class PlanEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    COMPLETE = "/complete/"
    ARCHIVE = "/archive/"
    TEMP = "/temp/"
    TEMP_ARCHIVE = "/temp/archive/"


@dataclass(frozen=True)
class UseCaseEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"


@dataclass(frozen=True)
class ContactEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"


@dataclass(frozen=True)
class TemplateRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"


@dataclass(frozen=True)
class LookupEndpointRoutes:
    BASE = "/"
    CONTENT_LOOKUP = "/content/"
    NUTRITION_THIRD_PARTY_LOOKUP = "/nutrition/third_party/"
    NUTRITION_AI_LOOKUP = "/nutrition/ai/"
    NUTRITION_IMAGE_AI_LOOKUP = "/nutrition/ai/image/"


@dataclass(frozen=True)
class DocumentEndpointRoutes:
    BASE = "/"
    FEED = "/feed/"
    ALL_DATA = "/all_data/"
    BY_QUERY = "/by_query/"


@dataclass(frozen=True)
class PlaceEndpointRoutes:
    BASE = "/"
    SEARCH = "/search/"
    ARCHIVE = "/archive/"
