from uuid import UUI<PERSON>

from fastapi import APIRouter, Depends
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.application.exceptions import BadRequestException, IncorrectOperationException, NoContentException
from services.data_service.api.constants import AnalyzeEndpointRoutes, DataServicePrefixes
from services.data_service.api.models.output.analyse_data_series_api_output import AnalyseDataSeriesAPIOutput
from services.data_service.api.models.output.event_correlation_api_output import EventCorrelationAPIOutput
from services.data_service.api.models.output.suggest_correlation_parameters_api_output import (
    SuggestCorrelationParametersAPIOutput,
)
from services.data_service.api.models.output.trend_detection_api_output import TrendDetectionAPIOutput
from services.data_service.api.models.request.analyze.analyse_data_series_api_request_input import (
    AnalyseDataSeriesAPIRequestInput,
)
from services.data_service.api.models.request.analyze.event_correlation_api_request_input import (
    EventCorrelationAPIRequestInput,
)
from services.data_service.api.models.request.analyze.suggest_correlation_parameters_api_request_input import (
    SuggestCorrelationParametersAPIRequestInput,
)
from services.data_service.api.models.request.analyze.trend_detection_api_request_input import (
    TrendDetectionAPIRequestInput,
)
from services.data_service.application.use_cases.analyse.analyse_data_series_use_case import (
    AnalyseDataSeriesUseCase,
    AnalyseDataSeriesUseCaseInputBoundary,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    EventCorrelationUseCase,
    EventCorrelationUseCaseInputBoundary,
)
from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_use_case import (
    SuggestCorrelationParametersUseCase,
    SuggestCorrelationParametersUseCaseInputBoundary,
)
from services.data_service.application.use_cases.trend_detection_use_case import (
    TrendDetectionUseCase,
    TrendDetectionUseCaseInputBoundary,
)

analyze_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.ANALYZE}",
    tags=["analyse"],
    responses={404: {"description": "Not found"}},
)


@analyze_router.post(
    AnalyzeEndpointRoutes.TREND_DETECT,
)
async def trend_detection_endpoint(
    input_boundary: TrendDetectionUseCaseInputBoundary = Depends(TrendDetectionAPIRequestInput.to_input_boundary),
    use_case: TrendDetectionUseCase = Injected(TrendDetectionUseCase),
) -> TrendDetectionAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    return TrendDetectionAPIOutput.map(model=result)


@analyze_router.post(
    AnalyzeEndpointRoutes.CORRELATE_EVENT,
)
async def event_correlation_endpoint(
    input_boundary: EventCorrelationUseCaseInputBoundary = Depends(EventCorrelationAPIRequestInput.to_input_boundary),
    use_case: EventCorrelationUseCase = Injected(EventCorrelationUseCase),
) -> EventCorrelationAPIOutput:
    try:
        result = await use_case.execute_async(input_boundary=input_boundary)
        if not result.data:
            raise NoContentException("No data available")
    except IncorrectOperationException as err:
        raise BadRequestException(message=err.message) from err
    except NoContentException as err:
        raise NoContentException(message=err.message) from err
    return EventCorrelationAPIOutput.map(model=result)


@analyze_router.post(
    AnalyzeEndpointRoutes.CORRELATE_EVENT_SUGGEST_PARAMETERS,
)
async def suggest_correlation_parameters_endpoint(
    _: UUID = Depends(get_current_uuid),
    input_boundary: SuggestCorrelationParametersUseCaseInputBoundary = Depends(
        SuggestCorrelationParametersAPIRequestInput.to_input_boundary
    ),
    use_case: SuggestCorrelationParametersUseCase = Injected(SuggestCorrelationParametersUseCase),
) -> SuggestCorrelationParametersAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    if not result:
        raise NoContentException("No suggestion available")
    return SuggestCorrelationParametersAPIOutput.map(model=result)


@analyze_router.post(
    AnalyzeEndpointRoutes.ANALYSE_DATA_SERIES,
)
async def analyse_data_series_endpoint(
    input_boundary: AnalyseDataSeriesUseCaseInputBoundary = Depends(AnalyseDataSeriesAPIRequestInput.to_input_boundary),
    use_case: AnalyseDataSeriesUseCase = Injected(AnalyseDataSeriesUseCase),
) -> AnalyseDataSeriesAPIOutput:
    result = await use_case.execute_async(input_boundary=input_boundary)
    return AnalyseDataSeriesAPIOutput.map(model=result)
