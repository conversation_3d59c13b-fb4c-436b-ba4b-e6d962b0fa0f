from typing import Any, AsyncGenerator, Awaitable, Callable, Dict

import pytest
from starlette import status

from services.base.domain.schemas.member_user.member_user import MemberUser
from services.data_service.api.tests.common_rpc_calls import _call_post_endpoint
from services.data_service.api.urls import AnalyzeEndpointUrls
from services.data_service.application.use_cases.analyse.models.data_models import DataOrigin


class TestAnalyseDataSeriesEndpoint:
    @pytest.fixture
    async def test_user(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[Any, dict]:
        user, headers = await user_headers_factory()
        yield headers

    passing_test_cases = [
        pytest.param(
            {
                "data_series": [10.0, 11.5, 10.2, 12.0, 10.8, 13.1, 11.9, 14.5],
                "x_labels": None,
                "data_origin_description": None,
                "data_series_description": None,
            },
            id="basic_data_series",
        ),
        pytest.param(
            {
                "data_series": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
                "x_labels": ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct"],
                "data_origin_description": None,
                "data_series_description": None,
            },
            id="with_x_labels",
        ),
        pytest.param(
            {
                "data_series": [50.5, 51.2, 49.8, 52.1, 50.9],
                "x_labels": None,
                "data_origin_description": DataOrigin.Other,
                "data_series_description": None,
            },
            id="with_origin_description",
        ),
        pytest.param(
            {
                "data_series": [100, 98, 105, 102, 99, 101],
                "x_labels": None,
                "data_origin_description": None,
                "data_series_description": "Daily stock prices for ABC Corp",
            },
            id="with_series_description",
        ),
        pytest.param(
            {
                "data_series": [20.1, 20.5, 20.0, 20.3, 20.7, 20.2],
                "x_labels": ["P1", "P2", "P3", "P4", "P5", "P6"],
                "data_origin_description": DataOrigin.Other,
                "data_series_description": "Hourly temperature fluctuations",
            },
            id="all_optional_fields",
        ),
        pytest.param(
            {
                "data_series": [0.1, 0.2],
                "x_labels": None,
                "data_origin_description": None,
                "data_series_description": None,
            },
            id="min_length_data_series",
        ),
    ]

    @pytest.mark.parametrize("request_payload", passing_test_cases)
    async def test_analyse_data_series_endpoint_passes(
        self,
        test_user: dict,
        request_payload: Dict[str, Any],
    ):
        headers = test_user
        response = await _call_post_endpoint(
            request_url=AnalyzeEndpointUrls.ANALYSE_DATA_SERIES,
            json=request_payload,
            headers=headers,
        )

        assert (
            response.status_code == status.HTTP_200_OK
        ), f"Expected 200 OK, got {response.status_code}. Response: {response.text}"
        response_data = response.json()

        expected_keys = [
            "anomalies",
            "trend_insight",
            "consistency_insight",
            "extreme_value_insight",
            "comparative_insights",
            "llm_insight",
        ]
        for key in expected_keys:
            assert key in response_data, f"Missing expected key: {key} in response"

    failing_test_cases = [
        pytest.param(
            {
                "data_series": [],
            },
            id="empty_data_series",
        ),
        pytest.param(
            {
                "data_series": [100.0],
            },
            id="single_value_data_series",
        ),
        pytest.param(
            {
                "x_labels": ["A", "B"],
            },
            id="missing_data_series",
        ),
        pytest.param(
            {
                "data_series": "not a list of numbers",
            },
            id="invalid_data_series_type",
        ),
        pytest.param(
            {
                "data_series": [1, 2, "not a float"],
            },
            id="data_series_with_non_float_element",
        ),
        pytest.param(
            {
                "data_series": [1, 2, 3],
                "x_labels": "not a list of strings",
            },
            id="invalid_x_labels_type",
        ),
        pytest.param(
            {
                "data_series": [1, 2, 3],
                "data_origin_description": 123,
            },
            id="invalid_origin_description_type",
        ),
    ]

    @pytest.mark.parametrize("request_payload", failing_test_cases)
    async def test_analyse_data_series_endpoint_fails(
        self,
        test_user: dict,
        request_payload: Dict[str, Any],
    ):
        headers = test_user
        response = await _call_post_endpoint(
            request_url=AnalyzeEndpointUrls.ANALYSE_DATA_SERIES,
            json=request_payload,
            headers=headers,
        )

        assert (
            response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        ), f"Expected 422, got {response.status_code}. Response: {response.text}"
