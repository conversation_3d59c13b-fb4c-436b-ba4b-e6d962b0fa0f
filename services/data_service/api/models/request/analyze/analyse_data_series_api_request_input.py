from __future__ import annotations

from uuid import UUID

from fastapi import Body, Depends

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.application.use_cases.analyse.analyse_data_series_use_case import (
    AnalyseDataSeriesUseCaseInputBoundary,
)


class AnalyseDataSeriesAPIRequestInput(AnalyseDataSeriesUseCaseInputBoundary):
    @staticmethod
    def to_input_boundary(
        request_input: AnalyseDataSeriesAPIRequestInput = Body(...),
        _: UUID = Depends(get_current_uuid),
    ) -> AnalyseDataSeriesUseCaseInputBoundary:
        return AnalyseDataSeriesUseCaseInputBoundary(
            data_series=request_input.data_series,
            x_labels=request_input.x_labels,
            data_origin_description=request_input.data_origin_description,
            data_series_description=request_input.data_series_description,
            should_call_llm=request_input.should_call_llm,
        )
