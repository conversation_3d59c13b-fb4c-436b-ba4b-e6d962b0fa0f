from asyncio import TaskGroup
from typing import Sequence

from services.base.application.utils.urls import join_as_url
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.document_base import AssetReference
from services.data_service.api.models.output.annotated_api_outputs import EventV3APIOutput, GroupV3APIOutput
from services.data_service.api.models.output.events.body_metric_api_outputs import (
    BloodGlucoseAPIOutput,
    BloodPressureAPIOutput,
    BodyMetricAPIOutput,
)
from services.data_service.api.models.output.events.content_api_outputs import ContentAPIOutput
from services.data_service.api.models.output.events.exercise_api_outputs import (
    CardioAPIOutput,
    ExerciseAPIOutput,
    StrengthAPIOutput,
)
from services.data_service.api.models.output.events.feeling_api_outputs import EmotionAPIOutput, StressAPIOutput
from services.data_service.api.models.output.events.medication_api_output import MedicationAPIOutput
from services.data_service.api.models.output.events.nutrition_api_outputs import (
    Drink<PERSON>IOutput,
    FoodAPIOutput,
    SupplementAPIOutput,
)
from services.data_service.api.models.output.events.sleep_v3_api_output import SleepV3APIOutput
from services.data_service.api.models.response.event.event_v3_api_output import (
    ActivityAPIOutput,
    EventGroupAPIOutput,
    NoteAPIOutput,
    PersonAPIOutput,
    PlaceVisitAPIOutput,
    SymptomAPIOutput,
)
from services.data_service.api.tests.common_rpc_calls import _call_get_endpoint
from services.data_service.api.urls import AssetsEndpointUrls
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.events.models.body_metric.insert_body_metric_inputs import (
    InsertBloodGlucoseInput,
    InsertBloodPressureInput,
    InsertBodyMetricInput,
)
from services.data_service.application.use_cases.events.models.body_metric.update_body_metric_inputs import (
    UpdateBloodGlucoseInput,
    UpdateBloodPressureInput,
    UpdateBodyMetricInput,
)
from services.data_service.application.use_cases.events.models.content.insert_content_inputs import InsertContentInput
from services.data_service.application.use_cases.events.models.content.update_content_inputs import UpdateContentInput
from services.data_service.application.use_cases.events.models.exercise.insert_exercise_inputs import (
    InsertCardioInput,
    InsertExerciseInput,
    InsertStrengthInput,
)
from services.data_service.application.use_cases.events.models.exercise.update_exercise_inputs import (
    UpdateCardioInput,
    UpdateExerciseInput,
    UpdateStrengthInput,
)
from services.data_service.application.use_cases.events.models.feeling.insert_feeling_inputs import (
    InsertEmotionInput,
    InsertStressInput,
)
from services.data_service.application.use_cases.events.models.feeling.update_feeling_inputs import (
    UpdateEmotionInput,
    UpdateStressInput,
)
from services.data_service.application.use_cases.events.models.insert_activity_input import InsertActivityInput
from services.data_service.application.use_cases.events.models.insert_event_group_input import InsertEventGroupInput
from services.data_service.application.use_cases.events.models.insert_event_input import InsertEventInput
from services.data_service.application.use_cases.events.models.insert_medication_input import InsertMedicationInput
from services.data_service.application.use_cases.events.models.insert_note_input import InsertNoteInput
from services.data_service.application.use_cases.events.models.insert_person_input import InsertPersonInput
from services.data_service.application.use_cases.events.models.insert_place_visit_input import InsertPlaceVisitInput
from services.data_service.application.use_cases.events.models.insert_sleep_v3_input import InsertSleepV3Input
from services.data_service.application.use_cases.events.models.insert_symptom_input import InsertSymptomInput
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import (
    InsertDrinkInput,
    InsertFoodInput,
    InsertSupplementInput,
)
from services.data_service.application.use_cases.events.models.nutrition.update_nutrition_inputs import (
    UpdateDrinkInput,
    UpdateFoodInput,
    UpdateSupplementInput,
)
from services.data_service.application.use_cases.events.models.shared import EventInputAsset, EventMetadataInput
from services.data_service.application.use_cases.events.models.update_activity_input import UpdateActivityInput
from services.data_service.application.use_cases.events.models.update_medication_input import UpdateMedicationInput
from services.data_service.application.use_cases.events.models.update_note_input import UpdateNoteInput
from services.data_service.application.use_cases.events.models.update_person_input import UpdatePersonInput
from services.data_service.application.use_cases.events.models.update_place_visit_input import UpdatePlaceVisitInput
from services.data_service.application.use_cases.events.models.update_sleep_v3_input import UpdateSleepV3Input
from services.data_service.application.use_cases.events.models.update_symptom_input import UpdateSymptomInput
from services.data_service.application.use_cases.events.updatable_event_inputs import UpdatableEventInputs
from services.data_service.type_resolver import TypeResolver


class EventAssertHelpers:

    @staticmethod
    def assert_events(
        returned_events: Sequence[EventV3APIOutput],
        expected_events: Sequence[InsertEventInputs | UpdatableEventInputs],
    ):
        assert len(returned_events) == len(expected_events)

        event_output: EventV3APIOutput
        event_input: InsertEventInputs | UpdatableEventInputs
        for event_output, event_input in zip(returned_events, expected_events):
            assert event_output.type == event_input.type
            assert event_output.category == event_output.category
            assert event_output.name == event_input.name
            assert event_output.timestamp == event_input.timestamp
            assert event_output.end_time == event_input.end_time
            assert event_output.note == event_input.note
            if not isinstance(
                event_input, (TypeResolver.INSERT_GROUP_INPUTS_UNION, TypeResolver.UPDATE_GROUP_INPUTS_UNION)
            ):
                assert not isinstance(event_output, EventGroupAPIOutput)
                assert event_output.category == event_input.category

            if event_output.end_time:
                assert event_output.duration == (event_output.end_time - event_output.timestamp).total_seconds()
            else:
                assert event_output.duration == 0
            assert event_output.tags == event_input.tags
            assert event_output.plan_extension == event_input.plan_extension
            if isinstance(event_input, InsertEventInput):
                assert event_output.template_id == event_input.template_id
            match event_input:
                # Body Metric Collection
                case InsertBloodPressureInput() | UpdateBloodPressureInput():
                    assert isinstance(event_output, BloodPressureAPIOutput)
                    assert event_output.systolic == event_input.systolic
                    assert event_output.diastolic == event_input.diastolic
                case InsertBloodGlucoseInput() | UpdateBloodGlucoseInput():
                    assert isinstance(event_output, BloodGlucoseAPIOutput)
                    assert event_output.value == event_input.value
                    assert event_output.specimen_source == event_input.specimen_source
                case InsertBodyMetricInput() | UpdateBodyMetricInput():
                    assert isinstance(event_output, BodyMetricAPIOutput)
                    assert event_output.value == event_input.value
                # Content Collection
                case InsertContentInput() | UpdateContentInput():
                    assert isinstance(event_output, ContentAPIOutput)
                    assert event_output.title == event_input.title
                    assert event_output.url == event_input.url
                    assert event_output.rating == event_input.rating

                # Exercise Collection
                case InsertCardioInput() | UpdateCardioInput():
                    assert isinstance(event_output, CardioAPIOutput)
                    assert event_output.distance == event_input.distance
                    assert event_output.elevation == event_input.elevation
                    assert event_output.rating == event_input.rating
                case InsertExerciseInput() | UpdateExerciseInput():
                    assert isinstance(event_output, ExerciseAPIOutput)
                    assert event_output.rating == event_input.rating
                case InsertStrengthInput() | UpdateStrengthInput():
                    assert isinstance(event_output, StrengthAPIOutput)
                    assert event_output.count == event_input.count
                    assert event_output.weight == event_input.weight
                    assert event_output.rating == event_input.rating
                # Feeling Collection
                case InsertEmotionInput() | UpdateEmotionInput():
                    assert isinstance(event_output, EmotionAPIOutput)
                    assert event_output.rating == event_input.rating
                case InsertStressInput() | UpdateStressInput():
                    assert isinstance(event_output, StressAPIOutput)
                    assert event_output.rating == event_input.rating

                # Nutrition Collection
                case InsertDrinkInput() | UpdateDrinkInput():
                    assert isinstance(event_output, DrinkAPIOutput)
                    assert event_output.brand == event_input.brand
                    assert event_output.rating == event_input.rating
                    assert event_output.consumed_type == event_input.consumed_type
                    assert event_output.consumed_amount == event_input.consumed_amount
                    assert event_output.calories == event_input.calories
                    assert event_output.flavor == event_input.flavor
                    assert event_output.nutrients == event_input.nutrients
                case InsertFoodInput() | UpdateFoodInput():
                    assert isinstance(event_output, FoodAPIOutput)
                    assert event_output.brand == event_input.brand
                    assert event_output.rating == event_input.rating
                    assert event_output.consumed_type == event_input.consumed_type
                    assert event_output.consumed_amount == event_input.consumed_amount
                    assert event_output.calories == event_input.calories
                    assert event_output.flavor == event_input.flavor
                    assert event_output.nutrients == event_input.nutrients
                case InsertSupplementInput() | UpdateSupplementInput():
                    assert isinstance(event_output, SupplementAPIOutput)
                    assert event_output.brand == event_input.brand
                    assert event_output.rating == event_input.rating
                    assert event_output.consumed_type == event_input.consumed_type
                    assert event_output.consumed_amount == event_input.consumed_amount
                    assert event_output.calories == event_input.calories
                    assert event_output.flavor == event_input.flavor
                    assert event_output.nutrients == event_input.nutrients
                case InsertSleepV3Input() | UpdateSleepV3Input():
                    assert isinstance(event_output, SleepV3APIOutput)
                    assert event_output.rating == event_input.rating
                    assert event_output.provider_score == event_input.provider_score
                    assert event_output.llif_score == event_input.llif_score
                    assert event_output.deep_seconds == event_input.deep_seconds
                    assert event_output.light_seconds == event_input.light_seconds
                    assert event_output.rem_seconds == event_input.rem_seconds
                    assert event_output.awake_seconds == event_input.awake_seconds
                    assert event_output.restless_moments == event_input.restless_moments

                # Other Events
                case InsertActivityInput() | UpdateActivityInput():
                    assert isinstance(event_output, ActivityAPIOutput)
                    assert event_output.rating == event_input.rating
                case InsertNoteInput() | UpdateNoteInput():
                    assert isinstance(event_output, NoteAPIOutput)
                    assert event_output.note == event_input.note
                case InsertSymptomInput() | UpdateSymptomInput():
                    assert isinstance(event_output, SymptomAPIOutput)
                    assert event_output.rating == event_input.rating
                    assert event_output.body_parts == event_input.body_parts
                case InsertEventGroupInput():
                    assert isinstance(event_output, GroupV3APIOutput)
                    assert event_output.note == event_input.note
                case InsertMedicationInput() | UpdateMedicationInput():
                    assert isinstance(event_output, MedicationAPIOutput)
                    assert event_output.single_dose_information == event_input.single_dose_information
                    assert event_output.consumed_amount == event_input.consumed_amount
                    assert event_output.consume_unit == event_input.consume_unit
                case InsertPersonInput():
                    assert isinstance(event_output, PersonAPIOutput)
                    assert event_output.rating == event_input.rating
                    assert event_output.contact_id == event_input.contact_id
                case UpdatePersonInput():
                    assert isinstance(event_output, PersonAPIOutput)
                    assert event_output.rating == event_input.rating
                case InsertPlaceVisitInput() | UpdatePlaceVisitInput():
                    assert isinstance(event_output, PlaceVisitAPIOutput)
                    assert event_output.name == event_input.name
                    assert event_output.rating == event_input.rating
                    assert event_output.place_id == event_input.place_id
                case _:
                    raise ShouldNotReachHereException(f"Unexpected type {type(event_input)}")

    @staticmethod
    async def validate_assets_content(
        input_assets: Sequence[EventInputAsset], output_assets: Sequence[AssetReference], headers: dict
    ):
        async def validate_asset_content(input_asset: EventInputAsset, output_asset: AssetReference):
            request_url = join_as_url(
                base_url=AssetsEndpointUrls.BY_ID, query_params={"asset_id": output_asset.asset_id}
            )
            response = await _call_get_endpoint(request_url=request_url, headers=headers)
            assert response
            assert response.content == input_asset.payload

        assert len(input_assets) == len(output_assets)
        async with TaskGroup() as group:
            for input_asset, output_asset in zip(input_assets, output_assets):
                group.create_task(coro=validate_asset_content(input_asset=input_asset, output_asset=output_asset))

    @staticmethod
    def assert_metadata(input_metadata: EventMetadataInput, output_events: Sequence[EventV3APIOutput]):
        for output_event in output_events:
            assert output_event.metadata.origin.value == input_metadata.origin.value
            assert output_event.metadata.source_service == input_metadata.source_service
