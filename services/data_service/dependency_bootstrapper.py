from typing import Self, Type

from aioboto3 import Session
from azure.storage.blob.aio import BlobServiceClient
from httpx import AsyncClient
from injector import Injector
from opensearchpy import AsyncOpenSearch
from pydantic_ai.models.groq import GroqModel
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.groq import GroqProvider
from pydantic_ai.providers.openai import OpenAIProvider
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import AsyncE<PERSON>ine, AsyncSession, async_sessionmaker, create_async_engine

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.depr_job_service import DeprJobService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.duplicate_check_service import DuplicateCheckService
from services.base.application.io.httpclient import HttpClient
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.application.object_storage_service import ObjectStorageService
from services.base.domain.enums.ai_models import AIModels
from services.base.domain.repository.contact_repository import ContactRepository
from services.base.domain.repository.document_repository import DocumentRepository
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_provider_repository import ExtensionProviderRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.extension_schema_repository import ExtensionSchemaRepository
from services.base.domain.repository.extension_subscriptions_repository import ExtensionSubscriptionsRepository
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_os_tasks_repository import MemberUserOSTasksRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.place_repository import PlaceRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.record_repository import RecordRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.infrastructure.aws.async_sns_wrapper import AsyncSNSWrapper
from services.base.infrastructure.azure.blob_storage_service import BlobStorageService
from services.base.infrastructure.database.opensearch.depr_opensearch_job_service import DeprOpenSearchJobService
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_aggregation_service import OSAggregationService
from services.base.infrastructure.database.opensearch.os_depr_event_repository import OSDeprEventRepository
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_contact_repository import OSContactRepository
from services.base.infrastructure.database.opensearch.repository.os_document_repository import OSDocumentRepository
from services.base.infrastructure.database.opensearch.repository.os_event_repository import OSEventRepository
from services.base.infrastructure.database.opensearch.repository.os_extension_result_repository import (
    OSExtensionResultRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_extension_run_repository import (
    OSExtensionRunRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_inbox_message_repository import (
    OSInboxMessageRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_place_repository import OSPlaceRepository
from services.base.infrastructure.database.opensearch.repository.os_plan_repository import OSPlanRepository
from services.base.infrastructure.database.opensearch.repository.os_record_repository import OSRecordRepository
from services.base.infrastructure.database.opensearch.repository.os_template_repository import (
    OSTemplateRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_use_case_repository import OSUseCaseRepository
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_detail_repository import (
    SqlAlchExtensionDetailRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_provider_repository import (
    SqlAlchExtensionProviderRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_schema_repository import (
    SqlAlchExtensionSchemaRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_subscriptions_repository import (
    SqlAlchExtensionSubscriptionsRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_device_repository import (
    SqlAlchMemberUserDeviceRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_os_tasks_repository import (
    SqlAlchMemberUserOSTasksRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_settings_repository import (
    SqlAlchMemberUserSettingsRepository,
)
from services.base.infrastructure.notifications.firebase.firebase_push_notification_service import (
    FirebasePushNotificationService,
)
from services.data_service.application.services.asset_service import AssetService
from services.data_service.application.services.environment_service import EnvironmentService
from services.data_service.application.use_cases.aggregate_location_use_case import AggregateLocationUseCase
from services.data_service.application.use_cases.ai.classify_event_category_use_case import ClassifyEventCategoryUseCase
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.classify_multi_event_type_use_case import (
    ClassifyMultiEventTypeUseCase,
)
from services.data_service.application.use_cases.ai.classify_user_action_use_case import ClassifyUserActionUseCase
from services.data_service.application.use_cases.ai.suggest_event_use_case import SuggestEventUseCase
from services.data_service.application.use_cases.ai.suggest_template_use_case import SuggestTemplateUseCase
from services.data_service.application.use_cases.analyse.analyse_data_series_use_case import AnalyseDataSeriesUseCase
from services.data_service.application.use_cases.analyse.llm_analyzer_use_case import LLMAnalyzerUseCase
from services.data_service.application.use_cases.by_id.delete_by_id_use_case import DeleteByIdUseCase
from services.data_service.application.use_cases.by_id.fetch_asset_by_id_use_case import FetchAssetByIdUseCase
from services.data_service.application.use_cases.by_id.fetch_asset_url_use_case import FetchAssetUrlUseCase
from services.data_service.application.use_cases.by_id.fetch_by_id_use_case import FetchByIdUseCase
from services.data_service.application.use_cases.by_id.update_by_id_use_case import UpdateByIdUseCase
from services.data_service.application.use_cases.calendar_aggregation.calendar_aggregation_lunar_phase_determinator import (
    LunarPhaseDeterminator,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_frequency_distribution_use_case import (
    CalendarFrequencyDistributionUseCase,
)
from services.data_service.application.use_cases.calendar_aggregation.calendar_histogram_aggregation_use_case import (
    CalendarHistogramAggregationUseCase,
)
from services.data_service.application.use_cases.contact.archive_contact_use_case import ArchiveContactUseCase
from services.data_service.application.use_cases.contact.insert_contact_use_case import InsertContactUseCase
from services.data_service.application.use_cases.contact.search_contact_use_case import SearchContactUseCase
from services.data_service.application.use_cases.contact.update_contact_use_case import UpdateContactUseCase
from services.data_service.application.use_cases.content.content_lookup_use_case import ContentLookupUseCase
from services.data_service.application.use_cases.content.handlers.content_handler_base import DefaultHandler
from services.data_service.application.use_cases.content.handlers.google_maps import GoogleMapsHandler
from services.data_service.application.use_cases.content.handlers.google_search import GoogleSearchHandler
from services.data_service.application.use_cases.content.handlers.wikipedia import WikipediaHandler
from services.data_service.application.use_cases.content.handlers.x import XHandler
from services.data_service.application.use_cases.content.handlers.youtube import YouTubeHandler
from services.data_service.application.use_cases.data_summary_use_case import DataSummaryUseCase
from services.data_service.application.use_cases.date_histogram_use_case import DateHistogramUseCase
from services.data_service.application.use_cases.depr_count_unique_tags_use_case import DeprCountUniqueTagsUseCase
from services.data_service.application.use_cases.environment.aggregate_air_quality_use_case import (
    AggregateAirQualityUseCase,
)
from services.data_service.application.use_cases.environment.aggregate_pollen_use_case import AggregatePollenUseCase
from services.data_service.application.use_cases.environment.aggregate_weather_use_case import AggregateWeatherUseCase
from services.data_service.application.use_cases.environment.fetch_environment_by_spacetime_use_case import (
    FetchEnvironmentBySpaceTimeUseCase,
)
from services.data_service.application.use_cases.environment.forecast_environment_use_case import (
    ForecastEnvironmentUseCase,
)
from services.data_service.application.use_cases.environment.forecast_summary_use_case import ForecastSummaryUseCase
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    EventCorrelationUseCase,
)
from services.data_service.application.use_cases.event_correlation.suggest_correlation_parameters_use_case import (
    SuggestCorrelationParametersUseCase,
)
from services.data_service.application.use_cases.events.delete_event_by_id_use_case import DeleteEventByIdUseCase
from services.data_service.application.use_cases.events.event_validators import (
    ContactReferencesValidator,
    DuplicatesValidator,
    GroupReferencesValidator,
    PlanReferencesValidator,
    TemplateReferencesValidator,
)
from services.data_service.application.use_cases.events.fetch_event_by_id_use_case import FetchEventByIdUseCase
from services.data_service.application.use_cases.events.insert_event_use_case import InsertEventUseCase
from services.data_service.application.use_cases.events.mappers.domain_event_transformer import (
    DomainEventTransformer,
)
from services.data_service.application.use_cases.events.modify_event_assets_use_case import ModifyEventAssetsUseCase
from services.data_service.application.use_cases.events.reorder_event_group_use_case import ReorderEventGroupUseCase
from services.data_service.application.use_cases.events.update_event_use_case import UpdateEventUseCase
from services.data_service.application.use_cases.extensions.delete_extension_run_use_case import (
    DeleteExtensionRunUseCase,
)
from services.data_service.application.use_cases.extensions.get_extension_detail_use_case import (
    GetExtensionDetailUseCase,
)
from services.data_service.application.use_cases.extensions.get_extension_provider_use_case import (
    GetExtensionProviderUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_details_use_case import (
    ListExtensionDetailsUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_providers_use_case import (
    ListExtensionProvidersUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_results_use_case import (
    ListExtensionResultsUseCase,
)
from services.data_service.application.use_cases.extensions.list_extension_runs_use_case import (
    ListExtensionRunsUseCase,
)
from services.data_service.application.use_cases.extensions.subscribe_extension_use_case import (
    SubscribeExtensionUseCase,
)
from services.data_service.application.use_cases.extensions.unsubscribe_extension_use_case import (
    UnsubscribeExtensionUseCase,
)
from services.data_service.application.use_cases.extensions.update_extension_run_status_use_case import (
    UpdateExtensionRunStatusUseCase,
)
from services.data_service.application.use_cases.feed.document_feed_use_case import DocumentFeedUseCase
from services.data_service.application.use_cases.frequency_distribution_use_case import FrequencyDistributionUseCase
from services.data_service.application.use_cases.helpers.do_list_query_helper import DoListQueryHelper
from services.data_service.application.use_cases.helpers.re_fetch_time_input_helper import ReFetchTimeInputHelper
from services.data_service.application.use_cases.list_heart_rate_use_case import ListHeartRateUseCase
from services.data_service.application.use_cases.list_location_use_case import ListLocationUseCase
from services.data_service.application.use_cases.list_resting_heart_rate_use_case import ListRestingHeartRateUseCase
from services.data_service.application.use_cases.list_shopping_activity_use_case import ListShoppingActivityUseCase
from services.data_service.application.use_cases.list_sleep_use_case import ListSleepUseCase
from services.data_service.application.use_cases.list_steps_use_case import ListStepsUseCase
from services.data_service.application.use_cases.loading.heart_rate.load_heart_rate_use_case import LoadHeartRateUseCase
from services.data_service.application.use_cases.loading.location.load_location_use_case import LoadLocationUseCase
from services.data_service.application.use_cases.loading.resting_heart_rate.load_resting_heart_rate_use_case import (
    LoadRestingHeartRateUseCase,
)
from services.data_service.application.use_cases.loading.sleep.load_sleep_use_case import LoadSleepUseCase
from services.data_service.application.use_cases.loading.steps.load_steps_use_case import LoadStepsUseCase
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_ai_use_case import (
    NutritionAIUseCase,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.nutrition_third_party_use_case import (
    NutritionThirdPartyProviderUseCase,
)
from services.data_service.application.use_cases.lookup.nutrition_provider.usda_provider import UsdaProvider
from services.data_service.application.use_cases.place.archive_place_use_case import ArchivePlaceUseCase
from services.data_service.application.use_cases.place.insert_place_use_case import InsertPlaceUseCase
from services.data_service.application.use_cases.place.search_place_use_case import SearchPlaceUseCase
from services.data_service.application.use_cases.place.update_place_use_case import UpdatePlaceUseCase
from services.data_service.application.use_cases.plans.archive_plans_use_case import ArchivePlansUseCase
from services.data_service.application.use_cases.plans.calculate_goal_progress_use_case import (
    CalculateGoalProgressUseCase,
)
from services.data_service.application.use_cases.plans.check_goal_to_recalculate_use_case import (
    CheckGoalsToRecalculateUseCase,
)
from services.data_service.application.use_cases.plans.complete_and_reset_goal_workflow import (
    CompleteAndResetGoalsWorkflow,
)
from services.data_service.application.use_cases.plans.complete_goal_use_case import CompleteGoalUseCase
from services.data_service.application.use_cases.plans.complete_plans_use_case import CompletePlansUseCase
from services.data_service.application.use_cases.plans.insert_plans_use_case import InsertPlansUseCase
from services.data_service.application.use_cases.plans.search_plans_use_case import SearchPlansUseCase
from services.data_service.application.use_cases.plans.update_plans_use_case import UpdatePlansUseCase
from services.data_service.application.use_cases.records.delete_record_by_id_use_case import DeleteRecordByIdUseCase
from services.data_service.application.use_cases.records.insert_record_use_case import InsertRecordUseCase
from services.data_service.application.use_cases.records.update_record_use_case import UpdateRecordUseCase
from services.data_service.application.use_cases.templates.archive_template_use_case import (
    ArchiveTemplateUseCase,
)
from services.data_service.application.use_cases.templates.insert_template_use_case import (
    InsertTemplateUseCase,
)
from services.data_service.application.use_cases.templates.search_template_use_case import SearchTemplatesUseCase
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator
from services.data_service.application.use_cases.templates.update_template_use_case import (
    UpdateTemplatesUseCase,
)
from services.data_service.application.use_cases.use_case.archive_use_case import ArchiveUseCase
from services.data_service.application.use_cases.use_case.insert_use_case import InsertUseCase
from services.data_service.application.use_cases.use_case.search_use_case import SearchUseCase
from services.data_service.application.use_cases.use_case.update_use_case import UpdateUseCase
from services.data_service.application.use_cases.user_data.delete_user_data_use_case import DeleteUserDataUseCase
from services.data_service.application.workflows.forecast_summary_workflow.forecast_summary_workflow import (
    ForecastSummaryWorkflow,
)
from services.data_service.constants import EPHEMERIS_PATH
from settings.app_config import settings
from settings.app_routes import AppRoutes
from settings.app_secrets import secrets


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: Type[T]) -> T:
        return self.injector.get(interface=interface)

    def _bind_singleton[T](self, interface: Type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def build(self) -> Self:
        self._bind_infrastructure()
        # TODO not sure about this
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_repositories()
        self._bind_services()
        self._bind_use_cases()
        self._bind_workflows()
        return self

    async def cleanup(self):
        await self.get(AsyncOpenSearch).close()
        await self.get(ObjectStorageService).close()

    def _bind_infrastructure(self):
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(interface=AsyncClient, to=AsyncClient())
        self._bind_singleton(interface=HttpClient, to=HttpClient(client=self.get(interface=AsyncClient)))
        self._bind_singleton(interface=Session, to=Session())
        self._bind_singleton(interface=AsyncMessageBrokerClient, to=AsyncSNSWrapper(session=self.get(Session)))

        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=EventRepository,
            to=OSEventRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=InboxMessageRepository,
            to=OSInboxMessageRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=RecordRepository,
            to=OSRecordRepository(
                client=self.get(interface=AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserOSTasksRepository,
            to=SqlAlchMemberUserOSTasksRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionSubscriptionsRepository,
            to=SqlAlchExtensionSubscriptionsRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionDetailRepository,
            to=SqlAlchExtensionDetailRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionSchemaRepository,
            to=SqlAlchExtensionSchemaRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=ExtensionProviderRepository,
            to=SqlAlchExtensionProviderRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=MemberUserDeviceRepository,
            to=SqlAlchMemberUserDeviceRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserSettingsRepository,
            to=SqlAlchMemberUserSettingsRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=TemplateRepository,
            to=OSTemplateRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=ExtensionRunRepository, to=OSExtensionRunRepository(client=self.get(AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=ExtensionResultRepository,
            to=OSExtensionResultRepository(
                client=self.get(AsyncOpenSearch), extension_run_repository=self.get(ExtensionRunRepository)
            ),
        )

        self._bind_singleton(
            interface=PlanRepository,
            to=OSPlanRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=UseCaseRepository,
            to=OSUseCaseRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=ContactRepository,
            to=OSContactRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=PlaceRepository,
            to=OSPlaceRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=DocumentRepository, to=OSDocumentRepository(client=self.get(interface=AsyncOpenSearch))
        )

    def _bind_services(self):
        self._bind_singleton(
            interface=AggregationService, to=OSAggregationService(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=DeprJobService, to=DeprOpenSearchJobService(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=DuplicateCheckService,
            to=DuplicateCheckService(search_service=self.get(interface=DocumentSearchService)),
        )
        self._bind_singleton(
            interface=ObjectStorageService,
            to=BlobStorageService(
                blob_service_client=BlobServiceClient.from_connection_string(secrets.ASSET_STORAGE_CREDENTIALS)
            ),
        )
        self._bind_singleton(
            interface=PushNotificationService,
            to=FirebasePushNotificationService(cred=secrets.FIREBASE_CREDENTIALS),
        )
        self._bind_singleton(
            interface=AssetService, to=AssetService(object_storage_service=self.get(interface=ObjectStorageService))
        )
        self._bind_singleton(
            interface=EnvironmentService,
            to=EnvironmentService(
                os_client=self.get(interface=AsyncOpenSearch), rest_client=self.get(interface=AsyncClient)
            ),
        )

    def _bind_use_cases(self):
        self._bind_assets_use_cases()
        self._bind_data_use_cases()
        self._bind_environment_use_cases()
        self._bind_extension_use_cases()
        self._bind_lookup_providers()
        self._bind_lookup_use_cases()

    def _bind_workflows(self):
        self._bind_singleton(
            interface=ForecastSummaryWorkflow,
            to=ForecastSummaryWorkflow(
                user_repository=self.get(interface=MemberUserRepository),
                forecast_summary_uc=self.get(interface=ForecastSummaryUseCase),
                push_notification_service=self.get(interface=PushNotificationService),
                device_repository=self.get(interface=MemberUserDeviceRepository),
                model=OpenAIModel(
                    model_name=AIModels.GPT_4_1_MINI, provider=OpenAIProvider(api_key=secrets.OPENAI_API_KEY)
                ),
                settings_repository=self.get(interface=MemberUserSettingsRepository),
            ),
        )
        self._bind_singleton(
            interface=CompleteAndResetGoalsWorkflow,
            to=CompleteAndResetGoalsWorkflow(
                search_service=self.get(interface=DocumentSearchService),
                complete_goal_uc=self.get(interface=CompleteGoalUseCase),
                member_user_device_repository=self.get(interface=MemberUserDeviceRepository),
                member_user_repository=self.get(interface=MemberUserRepository),
                push_notification_service=self.get(interface=PushNotificationService),
            ),
        )

    def _bind_assets_use_cases(self):
        self._bind_singleton(
            interface=FetchAssetByIdUseCase,
            to=FetchAssetByIdUseCase(object_storage_service=self.get(interface=ObjectStorageService)),
        )
        self._bind_singleton(
            interface=FetchAssetUrlUseCase,
            to=FetchAssetUrlUseCase(object_storage_service=self.get(interface=ObjectStorageService)),
        )
        self._bind_singleton(
            interface=AssetService,
            to=AssetService(object_storage_service=self.get(interface=ObjectStorageService)),
        )

    def _bind_extension_use_cases(self):
        self._bind_singleton(
            interface=ListExtensionRunsUseCase,
            to=ListExtensionRunsUseCase(extension_run_repo=self.get(interface=ExtensionRunRepository)),
        )

        self._bind_singleton(
            interface=ListExtensionResultsUseCase,
            to=ListExtensionResultsUseCase(
                extension_run_repo=self.get(interface=ExtensionRunRepository),
                extension_result_repo=self.get(interface=ExtensionResultRepository),
            ),
        )
        self._bind_singleton(
            interface=DeleteExtensionRunUseCase,
            to=DeleteExtensionRunUseCase(
                extension_run_repo=self.get(interface=ExtensionRunRepository),
                extension_result_repo=self.get(interface=ExtensionResultRepository),
            ),
        )
        self._bind_singleton(
            interface=UpdateExtensionRunStatusUseCase,
            to=UpdateExtensionRunStatusUseCase(
                extension_run_repo=self.get(interface=ExtensionRunRepository),
            ),
        )

        self._bind_singleton(
            interface=SubscribeExtensionUseCase,
            to=SubscribeExtensionUseCase(
                extension_subscriptions_repository=self.get(interface=ExtensionSubscriptionsRepository)
            ),
        )
        self._bind_singleton(
            interface=UnsubscribeExtensionUseCase,
            to=UnsubscribeExtensionUseCase(
                extension_subscriptions_repository=self.get(interface=ExtensionSubscriptionsRepository)
            ),
        )
        self._bind_singleton(
            interface=ListExtensionDetailsUseCase,
            to=ListExtensionDetailsUseCase(extension_repository=self.get(ExtensionDetailRepository)),
        )
        self._bind_singleton(
            interface=GetExtensionDetailUseCase,
            to=GetExtensionDetailUseCase(
                extension_detail_repository=self.get(ExtensionDetailRepository),
                extension_schema_repository=self.get(ExtensionSchemaRepository),
            ),
        )
        self._bind_singleton(
            interface=ListExtensionProvidersUseCase,
            to=ListExtensionProvidersUseCase(extension_provider_repository=self.get(ExtensionProviderRepository)),
        )
        self._bind_singleton(
            interface=GetExtensionProviderUseCase,
            to=GetExtensionProviderUseCase(extension_provider_repository=self.get(ExtensionProviderRepository)),
        )
        self._bind_singleton(
            interface=ListExtensionResultsUseCase,
            to=ListExtensionResultsUseCase(
                extension_result_repo=self.get(ExtensionResultRepository),
                extension_run_repo=self.get(ExtensionRunRepository),
            ),
        )

    def _bind_environment_use_cases(self):
        self._bind_singleton(
            interface=AggregateAirQualityUseCase,
            to=AggregateAirQualityUseCase(
                location_use_case=self.get(interface=AggregateLocationUseCase),
                environment_service=self.get(interface=EnvironmentService),
            ),
        )
        self._bind_singleton(
            interface=AggregateWeatherUseCase,
            to=AggregateWeatherUseCase(
                location_use_case=self.get(interface=AggregateLocationUseCase),
                environment_service=self.get(interface=EnvironmentService),
            ),
        )
        self._bind_singleton(
            interface=AggregatePollenUseCase,
            to=AggregatePollenUseCase(
                location_use_case=self.get(interface=AggregateLocationUseCase),
                environment_service=self.get(interface=EnvironmentService),
            ),
        )
        self._bind_singleton(
            interface=ForecastEnvironmentUseCase,
            to=ForecastEnvironmentUseCase(
                http_client=self.get(interface=HttpClient),
                aq_url=AppRoutes.FORECAST_AIR_QUALITY_V2_ROUTE,
                weather_url=AppRoutes.FORECAST_WEATHER_V2_ROUTE,
                pollen_url=AppRoutes.FORECAST_POLLEN_V2_ROUTE,
            ),
        )
        self._bind_singleton(
            interface=ForecastSummaryUseCase,
            to=ForecastSummaryUseCase(
                list_location_uc=self.get(interface=ListLocationUseCase),
                forecast_env_uc=self.get(interface=ForecastEnvironmentUseCase),
            ),
        )

        self._bind_singleton(
            interface=FetchEnvironmentBySpaceTimeUseCase,
            to=FetchEnvironmentBySpaceTimeUseCase(
                http_client=self.get(interface=HttpClient),
                aq_url=AppRoutes.AIR_QUALITY_V2_ROUTE,
                weather_url=AppRoutes.WEATHER_V2_ROUTE,
                pollen_url=AppRoutes.POLLEN_V2_ROUTE,
            ),
        )

    def _bind_data_use_cases(self):
        self._bind_events_v3_use_cases()

        self._bind_singleton(
            interface=DocumentFeedUseCase,
            to=DocumentFeedUseCase(search_service=self.get(interface=DocumentSearchService)),
        )

        self._bind_singleton(
            interface=CalculateGoalProgressUseCase,
            to=CalculateGoalProgressUseCase(
                plan_repo=self.get(interface=PlanRepository),
                agg_service=self.get(interface=AggregationService),
            ),
        )
        self._bind_singleton(
            interface=CompleteGoalUseCase,
            to=CompleteGoalUseCase(plan_repo=self.get(interface=PlanRepository)),
        )

        self._bind_singleton(
            interface=CheckGoalsToRecalculateUseCase,
            to=CheckGoalsToRecalculateUseCase(
                plan_repo=self.get(interface=PlanRepository),
                device_repo=self.get(interface=MemberUserDeviceRepository),
                search_service=self.get(interface=DocumentSearchService),
                calculate_goal_progress_uc=self.get(interface=CalculateGoalProgressUseCase),
                push_notification_service=self.get(interface=PushNotificationService),
            ),
        )

        self._bind_singleton(
            interface=ContentLookupUseCase,
            to=ContentLookupUseCase(
                handlers=[
                    XHandler(client=self.get(interface=AsyncClient)),
                    YouTubeHandler(),
                    WikipediaHandler(client=self.get(interface=AsyncClient)),
                    GoogleSearchHandler(client=self.get(interface=AsyncClient)),
                    GoogleMapsHandler(client=self.get(interface=AsyncClient)),
                    # IMPORTANT: Add new content handlers above DefaultHandler -- DefaultHandler must remain last (fallback)
                    DefaultHandler(client=self.get(interface=AsyncClient)),
                ],
                client=self.get(interface=AsyncClient),
            ),
        )

        self._bind_singleton(
            interface=ReFetchTimeInputHelper,
            to=ReFetchTimeInputHelper(search_service=self.get(interface=DocumentSearchService)),
        )
        self._bind_singleton(
            interface=DoListQueryHelper,
            to=DoListQueryHelper(
                re_fetcher=self.get(interface=ReFetchTimeInputHelper),
                search_service=self.get(interface=DocumentSearchService),
            ),
        )

        self._bind_singleton(
            interface=AggregateLocationUseCase,
            to=AggregateLocationUseCase(),
        )

        self._bind_singleton(
            interface=FetchByIdUseCase,
            to=FetchByIdUseCase(event_repo=self.get(interface=DeprEventRepository)),
        )
        self._bind_singleton(
            interface=DataSummaryUseCase,
            to=DataSummaryUseCase(search_service=self.get(interface=DocumentSearchService)),
        )
        self._bind_singleton(
            interface=DeprCountUniqueTagsUseCase,
            to=DeprCountUniqueTagsUseCase(agg_service=self.get(interface=AggregationService)),
        )
        self._bind_singleton(
            interface=FrequencyDistributionUseCase,
            to=FrequencyDistributionUseCase(agg_service=self.get(interface=AggregationService)),
        )
        self._bind_singleton(
            interface=DateHistogramUseCase,
            to=DateHistogramUseCase(agg_service=self.get(interface=AggregationService)),
        )
        self._bind_singleton(
            interface=EventCorrelationUseCase,
            to=EventCorrelationUseCase(
                agg_service=self.get(interface=AggregationService),
                search_service=self.get(interface=DocumentSearchService),
                environment_service=self.get(interface=EnvironmentService),
                location_use_case=self.get(interface=AggregateLocationUseCase),
            ),
        )

        self._bind_singleton(
            interface=SuggestCorrelationParametersUseCase,
            to=SuggestCorrelationParametersUseCase(
                model=OpenAIModel(
                    model_name=AIModels.GPT_4_1_MINI, provider=OpenAIProvider(api_key=secrets.OPENAI_API_KEY)
                ),
            ),
        )

        self._bind_singleton(
            interface=DeleteByIdUseCase,
            to=DeleteByIdUseCase(
                job_service=self.get(interface=DeprJobService),
                event_repo=self.get(interface=DeprEventRepository),
                tasks_repository=self.get(interface=MemberUserOSTasksRepository),
                fetch_by_id_use_case=self.get(interface=FetchByIdUseCase),
            ),
        )
        self._bind_singleton(
            interface=UpdateByIdUseCase,
            to=UpdateByIdUseCase(
                job_service=self.get(interface=DeprJobService),
                event_repo=self.get(interface=DeprEventRepository),
                tasks_repository=self.get(interface=MemberUserOSTasksRepository),
                fetch_by_id_use_case=self.get(interface=FetchByIdUseCase),
            ),
        )
        self._bind_singleton(
            interface=ListShoppingActivityUseCase,
            to=ListShoppingActivityUseCase(
                search_service=self.get(interface=DocumentSearchService),
                do_list_query=self.get(interface=DoListQueryHelper),
            ),
        )

        self._bind_singleton(
            interface=LunarPhaseDeterminator,
            to=LunarPhaseDeterminator(ephemeris_path=EPHEMERIS_PATH),
        )
        self._bind_singleton(
            interface=CalendarFrequencyDistributionUseCase,
            to=CalendarFrequencyDistributionUseCase(
                lunar_determinator=self.get(LunarPhaseDeterminator), search_service=self.get(DocumentSearchService)
            ),
        )
        self._bind_singleton(
            interface=CalendarHistogramAggregationUseCase,
            to=CalendarHistogramAggregationUseCase(
                lunar_determinator=self.get(LunarPhaseDeterminator), search_service=self.get(DocumentSearchService)
            ),
        )

        self._bind_singleton(
            interface=LoadLocationUseCase,
            to=LoadLocationUseCase(
                event_repo=self.get(interface=DeprEventRepository),
                search_service=self.get(interface=DocumentSearchService),
                message_broker=self.get(interface=AsyncMessageBrokerClient),
            ),
        )
        self._bind_singleton(
            interface=ListLocationUseCase,
            to=ListLocationUseCase(
                search_service=self.get(interface=DocumentSearchService),
                do_list_query=self.get(interface=DoListQueryHelper),
            ),
        )

        self._bind_singleton(
            interface=LoadHeartRateUseCase,
            to=LoadHeartRateUseCase(event_repo=self.get(interface=DeprEventRepository)),
        )
        self._bind_singleton(
            interface=ListHeartRateUseCase,
            to=ListHeartRateUseCase(
                search_service=self.get(interface=DocumentSearchService),
                do_list_query=self.get(interface=DoListQueryHelper),
            ),
        )

        self._bind_singleton(
            interface=LoadRestingHeartRateUseCase,
            to=LoadRestingHeartRateUseCase(event_repo=self.get(interface=DeprEventRepository)),
        )
        self._bind_singleton(
            interface=ListRestingHeartRateUseCase,
            to=ListRestingHeartRateUseCase(
                search_service=self.get(interface=DocumentSearchService),
                do_list_query=self.get(interface=DoListQueryHelper),
            ),
        )

        self._bind_singleton(
            interface=LoadSleepUseCase,
            to=LoadSleepUseCase(event_repo=self.get(interface=DeprEventRepository)),
        )
        self._bind_singleton(
            interface=ListSleepUseCase,
            to=ListSleepUseCase(
                search_service=self.get(interface=DocumentSearchService),
                do_list_query=self.get(interface=DoListQueryHelper),
            ),
        )

        self._bind_singleton(
            interface=LoadStepsUseCase,
            to=LoadStepsUseCase(event_repo=self.get(interface=DeprEventRepository)),
        )
        self._bind_singleton(
            interface=ListStepsUseCase,
            to=ListStepsUseCase(
                search_service=self.get(interface=DocumentSearchService),
                do_list_query=self.get(interface=DoListQueryHelper),
            ),
        )
        self._bind_singleton(
            interface=DeleteUserDataUseCase,
            to=DeleteUserDataUseCase(
                tasks_repository=self.get(interface=MemberUserOSTasksRepository),
                job_service=self.get(interface=DeprJobService),
                asset_service=self.get(interface=AssetService),
                user_repository=self.get(interface=MemberUserRepository),
                document_repository=self.get(interface=DocumentRepository),
                aggregation_service=self.get(interface=AggregationService),
            ),
        )

        # Templates
        self._bind_singleton(
            interface=TemplateValidator,
            to=TemplateValidator(
                template_repo=self.get(interface=TemplateRepository),
            ),
        )
        self._bind_singleton(
            interface=SearchTemplatesUseCase,
            to=SearchTemplatesUseCase(template_repo=self.get(interface=TemplateRepository)),
        )
        self._bind_singleton(
            interface=InsertTemplateUseCase,
            to=InsertTemplateUseCase(
                template_repo=self.get(interface=TemplateRepository),
                duplicate_validator=self.get(DuplicatesValidator),
                template_validator=self.get(TemplateValidator),
            ),
        )
        self._bind_singleton(
            interface=UpdateTemplatesUseCase,
            to=UpdateTemplatesUseCase(
                template_repo=self.get(interface=TemplateRepository),
                duplicate_validator=self.get(DuplicatesValidator),
                template_validator=self.get(TemplateValidator),
            ),
        )
        self._bind_singleton(
            interface=ArchiveTemplateUseCase,
            to=ArchiveTemplateUseCase(
                template_repo=self.get(interface=TemplateRepository),
                template_validator=self.get(TemplateValidator),
            ),
        )
        # Use cases
        self._bind_singleton(
            interface=SearchUseCase,
            to=SearchUseCase(use_case_repo=self.get(interface=UseCaseRepository)),
        )
        self._bind_singleton(
            interface=InsertUseCase,
            to=InsertUseCase(
                use_case_repo=self.get(interface=UseCaseRepository),
                duplicate_validator=self.get(DuplicatesValidator),
            ),
        )
        self._bind_singleton(
            interface=UpdateUseCase,
            to=UpdateUseCase(
                use_case_repo=self.get(interface=UseCaseRepository),
                duplicate_validator=self.get(DuplicatesValidator),
            ),
        )
        self._bind_singleton(
            interface=ArchiveUseCase,
            to=ArchiveUseCase(use_case_repo=self.get(interface=UseCaseRepository)),
        )

        # Contact use cases
        self._bind_singleton(
            interface=SearchContactUseCase,
            to=SearchContactUseCase(contact_repository=self.get(interface=ContactRepository)),
        )
        self._bind_singleton(
            interface=InsertContactUseCase,
            to=InsertContactUseCase(
                contact_repository=self.get(interface=ContactRepository),
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )
        self._bind_singleton(
            interface=UpdateContactUseCase,
            to=UpdateContactUseCase(
                contact_repository=self.get(interface=ContactRepository),
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )
        self._bind_singleton(
            interface=ArchiveContactUseCase,
            to=ArchiveContactUseCase(contact_repository=self.get(interface=ContactRepository)),
        )

        # Place use cases
        self._bind_singleton(
            interface=SearchPlaceUseCase,
            to=SearchPlaceUseCase(place_repository=self.get(interface=PlaceRepository)),
        )
        self._bind_singleton(
            interface=InsertPlaceUseCase,
            to=InsertPlaceUseCase(
                place_repository=self.get(interface=PlaceRepository),
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )
        self._bind_singleton(
            interface=UpdatePlaceUseCase,
            to=UpdatePlaceUseCase(
                place_repository=self.get(interface=PlaceRepository),
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )
        self._bind_singleton(
            interface=ArchivePlaceUseCase,
            to=ArchivePlaceUseCase(place_repository=self.get(interface=PlaceRepository)),
        )

        # AI
        self._bind_singleton(
            interface=ClassifyEventTypeUseCase,
            to=ClassifyEventTypeUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY))
            ),
        )

        self._bind_singleton(
            interface=ClassifyMultiEventTypeUseCase,
            to=ClassifyMultiEventTypeUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY))
            ),
        )

        self._bind_singleton(
            interface=ClassifyEventCategoryUseCase,
            to=ClassifyEventCategoryUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY))
            ),
        )

        self._bind_singleton(
            interface=SuggestTemplateUseCase,
            to=SuggestTemplateUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY)),
                search_templates_use_case=self.get(interface=SearchTemplatesUseCase),
            ),
        )

        self._bind_singleton(
            interface=SuggestEventUseCase,
            to=SuggestEventUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY)),
                suggest_template_use_case=self.get(interface=SuggestTemplateUseCase),
                member_user_settings_repo=self.get(interface=MemberUserSettingsRepository),
                classify_event_type_uc=self.get(interface=ClassifyEventTypeUseCase),
            ),
        )

        self._bind_singleton(
            interface=ClassifyUserActionUseCase,
            to=ClassifyUserActionUseCase(
                model=OpenAIModel(
                    model_name=AIModels.GPT_4_1_MINI, provider=OpenAIProvider(api_key=secrets.OPENAI_API_KEY)
                ),
            ),
        )

        # Plans
        self._bind_singleton(
            interface=InsertPlansUseCase,
            to=InsertPlansUseCase(
                template_repo=self.get(interface=TemplateRepository),
                plan_repo=self.get(interface=PlanRepository),
                duplicate_validator=self.get(DuplicatesValidator),
            ),
        )

        self._bind_singleton(
            interface=CompletePlansUseCase,
            to=CompletePlansUseCase(
                template_repo=self.get(interface=TemplateRepository),
                plan_repo=self.get(interface=PlanRepository),
                insert_event_uc=self.get(interface=InsertEventUseCase),
                template_validator=self.get(interface=TemplateValidator),
            ),
        )

        self._bind_singleton(
            interface=SearchPlansUseCase,
            to=SearchPlansUseCase(plan_repo=self.get(interface=PlanRepository)),
        )

        self._bind_singleton(
            interface=ArchivePlansUseCase,
            to=ArchivePlansUseCase(plan_repo=self.get(interface=PlanRepository)),
        )

        self._bind_singleton(
            interface=UpdatePlansUseCase,
            to=UpdatePlansUseCase(
                template_repo=self.get(interface=TemplateRepository),
                plan_repo=self.get(interface=PlanRepository),
                duplicate_validator=self.get(DuplicatesValidator),
            ),
        )

    def _bind_lookup_providers(self):
        self._bind_singleton(
            interface=UsdaProvider,
            to=UsdaProvider(
                lookup_url="https://api.nal.usda.gov/fdc/v1/foods/search",
                api_key=secrets.USDA_API_KEY,
                http_client=self.get(interface=HttpClient),
            ),
        )

    def _bind_lookup_use_cases(self):
        self._bind_singleton(
            interface=NutritionThirdPartyProviderUseCase,
            to=NutritionThirdPartyProviderUseCase(
                providers=[
                    self.get(interface=UsdaProvider),
                ],
            ),
        )
        self._bind_singleton(
            interface=NutritionAIUseCase,
            to=NutritionAIUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY)),
                classify_event_type_uc=self.get(interface=ClassifyEventTypeUseCase),
            ),
        )

    def _bind_events_v3_use_cases(self):
        self._bind_singleton(
            interface=TemplateReferencesValidator,
            to=TemplateReferencesValidator(
                template_repo=self.get(interface=TemplateRepository),
            ),
        )
        self._bind_singleton(
            interface=PlanReferencesValidator,
            to=PlanReferencesValidator(
                plan_repo=self.get(interface=PlanRepository),
            ),
        )
        self._bind_singleton(
            interface=ContactReferencesValidator,
            to=ContactReferencesValidator(
                contact_repo=self.get(interface=ContactRepository),
            ),
        )
        self._bind_singleton(
            interface=DuplicatesValidator,
            to=DuplicatesValidator(
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )
        self._bind_singleton(
            interface=GroupReferencesValidator,
            to=GroupReferencesValidator(
                event_repo=self.get(interface=EventRepository),
            ),
        )
        self._bind_singleton(
            interface=DomainEventTransformer,
            to=DomainEventTransformer(
                assets_service=self.get(interface=AssetService),
            ),
        )
        self._bind_singleton(
            interface=InsertEventUseCase,
            to=InsertEventUseCase(
                event_repo=self.get(interface=EventRepository),
                template_validator=self.get(interface=TemplateReferencesValidator),
                duplicate_validator=self.get(interface=DuplicatesValidator),
                event_mapper=self.get(interface=DomainEventTransformer),
                plan_validator=self.get(interface=PlanReferencesValidator),
                contact_validator=self.get(interface=ContactReferencesValidator),
                group_validator=self.get(interface=GroupReferencesValidator),
            ),
        )
        self._bind_singleton(
            interface=ModifyEventAssetsUseCase,
            to=ModifyEventAssetsUseCase(
                event_repo=self.get(interface=EventRepository),
                assets_service=self.get(interface=AssetService),
            ),
        )

        self._bind_singleton(
            interface=UpdateEventUseCase,
            to=UpdateEventUseCase(
                event_repo=self.get(interface=EventRepository),
                duplicate_validator=self.get(interface=DuplicatesValidator),
                group_validator=self.get(interface=GroupReferencesValidator),
            ),
        )

        self._bind_singleton(
            interface=DeleteEventByIdUseCase,
            to=DeleteEventByIdUseCase(
                event_repository=self.get(interface=EventRepository),
                asset_service=self.get(interface=AssetService),
            ),
        )

        self._bind_singleton(
            interface=FetchEventByIdUseCase,
            to=FetchEventByIdUseCase(
                event_repository=self.get(interface=EventRepository),
            ),
        )

        self._bind_singleton(
            interface=ReorderEventGroupUseCase,
            to=ReorderEventGroupUseCase(
                event_repo=self.get(interface=EventRepository),
            ),
        )

        self._bind_singleton(
            interface=InsertRecordUseCase,
            to=InsertRecordUseCase(
                record_repo=self.get(interface=RecordRepository),
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )

        self._bind_singleton(
            interface=UpdateRecordUseCase,
            to=UpdateRecordUseCase(
                record_repo=self.get(interface=RecordRepository),
                duplicate_check_service=self.get(interface=DuplicateCheckService),
            ),
        )

        self._bind_singleton(
            interface=DeleteRecordByIdUseCase,
            to=DeleteRecordByIdUseCase(
                record_repository=self.get(interface=RecordRepository),
            ),
        )

        self._bind_singleton(
            interface=LLMAnalyzerUseCase,
            to=LLMAnalyzerUseCase(
                model=GroqModel(model_name=AIModels.LLAMA4_SCOUT, provider=GroqProvider(api_key=secrets.GROQ_API_KEY))
            ),
        )

        self._bind_singleton(
            interface=AnalyseDataSeriesUseCase,
            to=AnalyseDataSeriesUseCase(
                llm_analyzer_use_case=self.get(interface=LLMAnalyzerUseCase),
            ),
        )
