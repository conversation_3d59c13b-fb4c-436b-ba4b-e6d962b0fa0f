from typing import Literal
from uuid import uuid4

from pydantic import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.content_hash import Hasher
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content import (
    Content,
    ContentCategory,
    ContentFields,
    ContentIdentifier,
    ContentValueLimits,
)
from services.base.domain.schemas.events.document_base import RBACSchema
from services.data_service.application.use_cases.events.models.insert_event_input import (
    EventInsertionContext,
    InsertEventInput,
)


class InsertContentInput(InsertEventInput, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY)
    title: NonEmptyStr | None = Field(
        alias=ContentFields.TITLE, max_length=ContentValueLimits.TITLE_MAX_LENGTH, default=None
    )
    url: NonEmptyStr | None = Field(alias=ContentFields.URL, default=None, max_length=ContentValueLimits.URL_MAX_LENGTH)
    rating: int | None = Field(
        alias=ContentFields.RATING,
        ge=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
        le=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
        default=None,
    )
    note: NonEmptyStr | None = Field(alias=ContentFields.NOTE, default=None)

    @computed_field()
    @property
    def content_hash(self) -> str:
        return Hasher.fields_sha256(
            fields=[
                self.name,
                self.timestamp.isoformat(),
                self.url,
                self.title,
            ]
        )

    def to_domain(self, ctx: EventInsertionContext) -> Content:
        return Content(
            # technical
            type=DataType.Content,
            rbac=RBACSchema(owner_id=ctx.owner_id),
            group_id=ctx.group_id,
            submission_id=ctx.submission_id,
            template_id=self.template_id,
            id=uuid4(),
            category=self.category,
            plan_extension=self.plan_extension,
            # common
            timestamp=self.timestamp,
            end_time=self.end_time,
            metadata=ctx.metadata,
            tags=self.tags,
            asset_references=ctx.asset_references,
            name=self.name,
            title=self.title,
            url=self.url,
            rating=self.rating,
            note=self.note,
        )
