from typing import List, Sequence

import numpy as np

from services.data_service.application.use_cases.analyse.models.data_models import Anomaly, AnomalyType


class AnomalyDetection:
    @staticmethod
    def iqr_detect(data_series: Sequence[float], x_labels: Sequence[str] | None = None, k: float = 1) -> List[Anomaly]:
        """
        Detects outliers using the Interquartile Range (IQR) method and returns Anomaly objects.
        """
        anomalies = []
        data_series_np = np.array(data_series)
        if data_series_np.size == 0:
            return []

        if np.all(data_series_np == data_series_np[0]) or len(data_series_np) < 2:
            return []

        Q1 = np.percentile(data_series_np, 25)
        Q3 = np.percentile(data_series_np, 75)

        IQR = Q3 - Q1

        if IQR == 0:
            unique_values = np.unique(data_series_np)
            if len(unique_values) <= 1:
                return []
            lower_bound = Q1
            upper_bound = Q3
        else:
            lower_bound = Q1 - k * IQR
            upper_bound = Q3 + k * IQR

        for i, x in enumerate(data_series_np):
            anomaly_type = None
            description = ""
            is_anomaly = False
            if x < lower_bound:
                deviation = lower_bound - x
                description = f"Low outlier: {x:.1f} (below lower bound {lower_bound:.1f} by {deviation:.1f})"
                anomaly_type = AnomalyType.DIP
                is_anomaly = True
            elif x > upper_bound:
                deviation = x - upper_bound
                description = f"High outlier: {x:.1f} (above upper bound {upper_bound:.1f} by {deviation:.1f})"
                anomaly_type = AnomalyType.SPIKE
                is_anomaly = True

            if is_anomaly and anomaly_type is not None:
                if x_labels and i < len(x_labels):
                    description += f" on {x_labels[i]}"
                description += "."
                anomalies.append(
                    Anomaly(
                        index=i,
                        label=x_labels[i] if x_labels and i < len(x_labels) else None,
                        value=x,
                        anomaly_type=anomaly_type,
                        description=description,
                    )
                )
        return anomalies
