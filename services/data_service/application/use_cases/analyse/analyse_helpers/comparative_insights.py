from typing import List, Optional

import numpy as np

from services.data_service.application.use_cases.analyse.models.data_models import (
    ComparativeInsight,
    ExtremeValueInsight,
)


class ComparativeInsights:
    @staticmethod
    def identify_extremes(data: list[float], x_labels: Optional[list[str]] = None) -> ExtremeValueInsight:
        data_np = np.array(data)
        if not data_np.size:
            return ExtremeValueInsight(
                max_value=float("nan"),
                max_indices=[],
                max_labels=[],
                min_value=float("nan"),
                min_indices=[],
                min_labels=[],
                description="No data available to identify extremes.",
            )

        min_value = np.min(data_np)
        max_value = np.max(data_np)

        min_indices = [i for i, val in enumerate(data_np) if val == min_value]
        max_indices = [i for i, val in enumerate(data_np) if val == max_value]

        min_labels = [x_labels[i] for i in min_indices] if x_labels else [f"index {i}" for i in min_indices]
        max_labels = [x_labels[i] for i in max_indices] if x_labels else [f"index {i}" for i in max_indices]

        description = f"Your highest value is {max_value:.1f} on {', '.join(max_labels)} and your lowest value is {min_value:.1f} on {', '.join(min_labels)}."

        return ExtremeValueInsight(
            max_value=float(max_value),
            max_indices=max_indices,
            max_labels=max_labels,
            min_value=float(min_value),
            min_indices=min_indices,
            min_labels=min_labels,
            description=description,
        )

    @staticmethod
    def comparison_to_average(
        data: list[float], x_labels: Optional[list[str]] = None, threshold_deviation_percent: float = 50.0
    ) -> List[ComparativeInsight]:
        data_np = np.array(data)
        if data_np.size < 2:
            return []

        data_mean = np.mean(data_np)
        significant_deviations = []

        for i, val in enumerate(data_np):
            if data_mean == 0:
                deviation_percent = float("inf") if val != 0 else 0
            else:
                deviation_percent = (abs(val - data_mean) / data_mean) * 100

            if deviation_percent > threshold_deviation_percent:
                context_label = x_labels[i] if x_labels else f"index {i}"
                description = ""
                if val > data_mean:
                    description = (
                        f"Value {val:.1f} on {context_label} is significantly above average ({data_mean:.1f})."
                    )
                else:
                    description = (
                        f"Value {val:.1f} on {context_label} is significantly below average ({data_mean:.1f})."
                    )

                significant_deviations.append(
                    ComparativeInsight(
                        index=i,
                        label=x_labels[i] if x_labels else None,
                        value=float(val),
                        average=float(data_mean),
                        deviation_percent=float(deviation_percent),
                        description=description,
                    )
                )
        return significant_deviations
