import numpy as np
import pytest

from services.data_service.application.use_cases.analyse.analyse_helpers.pattern_recognition import PatternRecognition
from services.data_service.application.use_cases.analyse.models.data_models import (
    ConsistencyInsight,
    ConsistencyLevel,
    TrendInsight,
    TrendSegment,
    TrendType,
)


@pytest.mark.parametrize(
    "data, x_labels, window_size, significant_slope_threshold, trend_change_persistence_threshold, expected_insight",
    [
        pytest.param(
            [1, 2],
            None,
            5,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.STABLE,
                description="",
                slope=None,
                intercept=None,
                relative_slope=None,
                start_label=None,
                end_label=None,
                trend_segments=[],
            ),
            id="not_enough_data_n_lt_3",
        ),
        pytest.param(
            [1, 2, 3, 4],
            None,
            5,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.INCREASING,
                description="",
                slope=1.0,
                intercept=1.0,
                relative_slope=0.3333333333333333,
                start_label=None,
                end_label=None,
                trend_segments=[],
            ),
            id="data_length_lt_window_size",
        ),
        pytest.param(
            [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            None,
            5,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.INCREASING,
                description="",
                slope=1.0,
                intercept=1.0,
                relative_slope=0.1111111111111111,
                start_label=None,
                end_label=None,
                trend_segments=[
                    TrendSegment(
                        start_index=0,
                        end_index=9,
                        trend_type=TrendType.INCREASING,
                        slope=1.0,
                    )
                ],
            ),
            id="simple_increasing_trend_no_segments",
        ),
        pytest.param(
            [10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
            None,
            5,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.DECREASING,
                description="",
                slope=-1.0,
                intercept=10.0,
                relative_slope=0.1111111111111111,
                start_label=None,
                end_label=None,
                trend_segments=[
                    TrendSegment(
                        start_index=0,
                        end_index=9,
                        trend_type=TrendType.DECREASING,
                        slope=-1.0,
                    )
                ],
            ),
            id="simple_decreasing_trend_no_segments",
        ),
        pytest.param(
            [5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
            None,
            5,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.STABLE,
                description="",
                slope=0.0,
                intercept=5.0,
                relative_slope=0.0,
                start_label=None,
                end_label=None,
                trend_segments=[],
            ),
            id="stable_trend_no_segments",
        ),
        pytest.param(
            [1, 2, 3, 4, 5, 4, 3, 2, 1],
            None,
            3,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.INCREASING,
                description="",
                slope=0,
                intercept=2.777778,
                relative_slope=0,
                start_label=None,
                end_label=None,
                trend_segments=[
                    TrendSegment(start_index=0, end_index=2, trend_type=TrendType.INCREASING, slope=1.0),
                    TrendSegment(start_index=3, end_index=8, trend_type=TrendType.DECREASING, slope=-0.714286),
                ],
            ),
            id="trend_change_persistence_met",
        ),
        pytest.param(
            [1, 2, 3, 4, 5, 10, 9, 10, 11],
            None,
            3,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.INCREASING,
                description="",
                slope=1.366667,
                intercept=0.644444,
                relative_slope=0.136667,
                start_label=None,
                end_label=None,
                trend_segments=[
                    TrendSegment(
                        start_index=0,
                        end_index=8,
                        trend_type=TrendType.INCREASING,
                        slope=1.3666666666666663,
                    ),
                ],
            ),
            id="trend_change_persistence_not_met",
        ),
        pytest.param(
            [1, 2, 3, 4, 5],
            ["A", "B", "C", "D", "E"],
            3,
            0.1,
            2,
            TrendInsight(
                overall_trend=TrendType.INCREASING,
                description="",
                slope=1.0,
                intercept=1.0,
                relative_slope=0.25,
                start_label="A",
                end_label="E",
                trend_segments=[
                    TrendSegment(
                        start_index=0,
                        end_index=4,
                        trend_type=TrendType.INCREASING,
                        slope=0.9999999999999997,
                    )
                ],
            ),
            id="with_x_labels_increasing_trend",
        ),
    ],
)
def test_trend_analysis_dynamic(
    data, x_labels, window_size, significant_slope_threshold, trend_change_persistence_threshold, expected_insight
):
    actual_insight = PatternRecognition.trend_analysis_dynamic(
        data, x_labels, window_size, significant_slope_threshold, trend_change_persistence_threshold
    )

    num_decimal_places = 6

    assert actual_insight.overall_trend == expected_insight.overall_trend

    if expected_insight.slope is None:
        assert actual_insight.slope is None
    else:
        assert round(actual_insight.slope, num_decimal_places) == round(  # pyright: ignore
            expected_insight.slope, num_decimal_places
        )  # pyright: ignore

    if expected_insight.intercept is None:
        assert actual_insight.intercept is None
    else:
        assert round(actual_insight.intercept, num_decimal_places) == round(  # pyright: ignore
            expected_insight.intercept, num_decimal_places
        )

    if expected_insight.relative_slope is None:
        assert actual_insight.relative_slope is None
    else:
        assert round(actual_insight.relative_slope, num_decimal_places) == round(  # pyright: ignore
            expected_insight.relative_slope, num_decimal_places
        )

    assert actual_insight.start_label == expected_insight.start_label
    assert actual_insight.end_label == expected_insight.end_label

    assert len(actual_insight.trend_segments) == len(expected_insight.trend_segments)

    actual_segments_sorted = sorted(actual_insight.trend_segments, key=lambda s: s.start_index)
    expected_segments_sorted = sorted(expected_insight.trend_segments, key=lambda s: s.start_index)

    for actual_seg, expected_seg in zip(actual_segments_sorted, expected_segments_sorted):
        assert actual_seg.start_index == expected_seg.start_index
        assert actual_seg.end_index == expected_seg.end_index
        assert actual_seg.trend_type == expected_seg.trend_type
        assert round(actual_seg.slope, num_decimal_places) == round(expected_seg.slope, num_decimal_places)


@pytest.mark.parametrize(
    "data, x_labels, expected_insight",
    [
        pytest.param(
            [],
            None,
            ConsistencyInsight(level=ConsistencyLevel.INCONSISTENT, description="", mean_value=None, std_dev=None),
            id="empty_data",
        ),
        pytest.param(
            [0, 0, 0, 0],
            None,
            ConsistencyInsight(level=ConsistencyLevel.PERFECTLY_UNIFORM, description="", mean_value=0.0, std_dev=0.0),
            id="perfectly_uniform_all_zeros",
        ),
        pytest.param(
            [5, 5, 5, 5],
            None,
            ConsistencyInsight(level=ConsistencyLevel.PERFECTLY_UNIFORM, description="", mean_value=5.0, std_dev=0.0),
            id="perfectly_uniform_same_non_zeros",
        ),
        pytest.param(
            [100, 101, 99, 100, 102],
            None,
            ConsistencyInsight(
                level=ConsistencyLevel.HIGHLY_CONSISTENT, description="", mean_value=100.4, std_dev=1.019803902718557
            ),
            id="highly_consistent_low_cv",
        ),
        pytest.param(
            [100, 105, 90, 110, 95],
            None,
            ConsistencyInsight(
                level=ConsistencyLevel.MODERATELY_CONSISTENT,
                description="",
                mean_value=100.0,
                std_dev=7.0710678118654755,
            ),
            id="moderately_consistent_moderate_cv",
        ),
        pytest.param(
            [10, 50, 5, 100, 20],
            None,
            ConsistencyInsight(
                level=ConsistencyLevel.INCONSISTENT, description="", mean_value=37.0, std_dev=35.156791662493895
            ),
            id="inconsistent_high_cv",
        ),
        pytest.param(
            [0, 1, 0, -1, 0],
            None,
            ConsistencyInsight(
                level=ConsistencyLevel.INCONSISTENT, description="", mean_value=0.0, std_dev=0.6324555320336759
            ),
            id="zero_centric_variations",
        ),
        pytest.param(
            [10, 10.1, 9.9, 10, 10.2, 9.8, 10.05],
            ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
            ConsistencyInsight(
                level=ConsistencyLevel.HIGHLY_CONSISTENT,
                description="",
                mean_value=10.007142857142858,
                std_dev=0.12079667518062649,
            ),
            id="highly_consistent_with_x_labels_days",
        ),
        pytest.param(
            [100, 100.5, 99.5, 100, 100.1, 99.9, 100.2, 99.8, 100.3, 99.7, 100.4, 99.6],
            [str(i) for i in range(12)],
            ConsistencyInsight(
                level=ConsistencyLevel.PERFECTLY_UNIFORM, description="", mean_value=100.0, std_dev=0.3027650354097491
            ),
            id="perfectly_uniform_with_x_labels_months",
        ),
        pytest.param(
            [0.001, 0.001, 0.001],
            None,
            ConsistencyInsight(level=ConsistencyLevel.PERFECTLY_UNIFORM, description="", mean_value=0.001, std_dev=0.0),
            id="small_non_zero_mean_low_std_dev_perfectly_uniform",
        ),
        pytest.param(
            [0.001, 0.0015, 0.0005],
            None,
            ConsistencyInsight(
                level=ConsistencyLevel.INCONSISTENT, description="", mean_value=0.001, std_dev=0.000408248290463863
            ),
            id="small_non_zero_mean_small_std_dev_inconsistent",
        ),
    ],
)
def test_consistency_check(data, x_labels, expected_insight):
    actual_insight = PatternRecognition.consistency_check(data, x_labels)

    assert actual_insight.level == expected_insight.level

    if expected_insight.mean_value is None:
        assert actual_insight.mean_value is None
    else:
        assert np.isclose(actual_insight.mean_value, expected_insight.mean_value)  # pyright: ignore

    if expected_insight.std_dev is None:
        assert actual_insight.std_dev is None
    else:
        assert np.isclose(actual_insight.std_dev, expected_insight.std_dev)  # pyright: ignore
