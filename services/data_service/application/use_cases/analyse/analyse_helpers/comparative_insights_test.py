import pytest

from services.data_service.application.use_cases.analyse.analyse_helpers.comparative_insights import ComparativeInsights
from services.data_service.application.use_cases.analyse.models.data_models import (
    ComparativeInsight,
    ExtremeValueInsight,
)


@pytest.mark.parametrize(
    "data, x_labels, expected_insight",
    [
        pytest.param(
            [5],
            None,
            ExtremeValueInsight(
                max_value=5.0,
                max_indices=[0],
                max_labels=["index 0"],
                min_value=5.0,
                min_indices=[0],
                min_labels=["index 0"],
                description="",
            ),
            id="single_element_list",
        ),
        pytest.param(
            [1, 2, 3, 4, 5],
            None,
            ExtremeValueInsight(
                max_value=5.0,
                max_indices=[4],
                max_labels=["index 4"],
                min_value=1.0,
                min_indices=[0],
                min_labels=["index 0"],
                description="",
            ),
            id="ascending_order",
        ),
        pytest.param(
            [10, 1, 5, 10, 2, 1],
            None,
            ExtremeValueInsight(
                max_value=10.0,
                max_indices=[0, 3],
                max_labels=["index 0", "index 3"],
                min_value=1.0,
                min_indices=[1, 5],
                min_labels=["index 1", "index 5"],
                description="",
            ),
            id="multiple_min_max_no_labels",
        ),
        pytest.param(
            [10, 1, 5, 10, 2, 1],
            ["A", "B", "C", "D", "E", "F"],
            ExtremeValueInsight(
                max_value=10.0,
                max_indices=[0, 3],
                max_labels=["A", "D"],
                min_value=1.0,
                min_indices=[1, 5],
                min_labels=["B", "F"],
                description="",
            ),
            id="multiple_min_max_with_labels",
        ),
        pytest.param(
            [7, 7, 7, 7],
            None,
            ExtremeValueInsight(
                max_value=7.0,
                max_indices=[0, 1, 2, 3],
                max_labels=["index 0", "index 1", "index 2", "index 3"],
                min_value=7.0,
                min_indices=[0, 1, 2, 3],
                min_labels=["index 0", "index 1", "index 2", "index 3"],
                description="",
            ),
            id="all_values_same",
        ),
        pytest.param(
            [-5, -1, -10, -2],
            None,
            ExtremeValueInsight(
                max_value=-1.0,
                max_indices=[1],
                max_labels=["index 1"],
                min_value=-10.0,
                min_indices=[2],
                min_labels=["index 2"],
                description="",
            ),
            id="negative_values",
        ),
        pytest.param(
            [-5, 10, 0, -10, 5],
            None,
            ExtremeValueInsight(
                max_value=10.0,
                max_indices=[1],
                max_labels=["index 1"],
                min_value=-10.0,
                min_indices=[3],
                min_labels=["index 3"],
                description="",
            ),
            id="mixed_positive_negative",
        ),
        pytest.param(
            [1.1, 2.5, 0.9, 3.7, 2.5],
            None,
            ExtremeValueInsight(
                max_value=3.7,
                max_indices=[3],
                max_labels=["index 3"],
                min_value=0.9,
                min_indices=[2],
                min_labels=["index 2"],
                description="",
            ),
            id="float_values",
        ),
        pytest.param(
            [1.0, 5.0, 2.0, 5.0, 1.0],
            ["a", "b", "c", "d", "e"],
            ExtremeValueInsight(
                max_value=5.0,
                max_indices=[1, 3],
                max_labels=["b", "d"],
                min_value=1.0,
                min_indices=[0, 4],
                min_labels=["a", "e"],
                description="",
            ),
            id="float_values_with_labels",
        ),
        pytest.param(
            [1000, 500, 10000, 200, 5000],
            None,
            ExtremeValueInsight(
                max_value=10000.0,
                max_indices=[2],
                max_labels=["index 2"],
                min_value=200.0,
                min_indices=[3],
                min_labels=["index 3"],
                description="",
            ),
            id="large_values",
        ),
    ],
)
def test_identify_extremes(data, x_labels, expected_insight):
    actual_insight = ComparativeInsights.identify_extremes(data, x_labels)

    assert actual_insight.max_value == expected_insight.max_value
    assert actual_insight.max_indices == expected_insight.max_indices
    assert actual_insight.max_labels == expected_insight.max_labels
    assert actual_insight.min_value == expected_insight.min_value
    assert actual_insight.min_indices == expected_insight.min_indices
    assert actual_insight.min_labels == expected_insight.min_labels


@pytest.mark.parametrize(
    "data, x_labels, threshold_deviation_percent, expected_insights",
    [
        pytest.param([], None, 50.0, [], id="empty_data"),
        pytest.param([10], None, 50.0, [], id="single_element_data"),
        pytest.param([10, 11, 12, 10, 9], None, 50.0, [], id="no_significant_deviation"),
        pytest.param(
            [10, 20, 5, 30, 15],
            None,
            50.0,
            [
                ComparativeInsight(
                    index=2, label=None, value=5.0, average=16.0, deviation_percent=68.75, description=""
                ),
                ComparativeInsight(
                    index=3, label=None, value=30.0, average=16.0, deviation_percent=87.5, description=""
                ),
            ],
            id="some_deviations_50_percent",
        ),
        pytest.param(
            [90, 10, 90, 20, 80],
            None,
            50.0,
            [
                ComparativeInsight(
                    index=0, value=90.0, average=58.0, deviation_percent=55.17241379310345, description=""
                ),
                ComparativeInsight(
                    index=1, value=10.0, average=58.0, deviation_percent=82.75862068965517, description=""
                ),
                ComparativeInsight(
                    index=2, value=90.0, average=58.0, deviation_percent=55.17241379310345, description=""
                ),
                ComparativeInsight(
                    index=3, value=20.0, average=58.0, deviation_percent=65.51724137931035, description=""
                ),
            ],
            id="multiple_deviations_50_percent_mixed",
        ),
        pytest.param(
            [10, 100, 5, 1, 200],
            ["A", "B", "C", "D", "E"],
            50.0,
            [
                ComparativeInsight(
                    index=0, label="A", value=10.0, average=63.2, deviation_percent=84.17721518987342, description=""
                ),
                ComparativeInsight(
                    index=1, label="B", value=100.0, average=63.2, deviation_percent=58.22784810126582, description=""
                ),
                ComparativeInsight(
                    index=2, label="C", value=5.0, average=63.2, deviation_percent=92.08860759493672, description=""
                ),
                ComparativeInsight(
                    index=3, label="D", value=1.0, average=63.2, deviation_percent=98.41772151898734, description=""
                ),
                ComparativeInsight(
                    index=4, label="E", value=200.0, average=63.2, deviation_percent=216.4556962025316, description=""
                ),
            ],
            id="all_deviations_with_labels",
        ),
        pytest.param(
            [10, 20, 5, 30, 15],
            None,
            20.0,
            [
                ComparativeInsight(index=0, value=10.0, average=16.0, deviation_percent=37.5, description=""),
                ComparativeInsight(index=1, value=20.0, average=16.0, deviation_percent=25.0, description=""),
                ComparativeInsight(index=2, value=5.0, average=16.0, deviation_percent=68.75, description=""),
                ComparativeInsight(index=3, value=30.0, average=16.0, deviation_percent=87.5, description=""),
            ],
            id="more_deviations_20_percent",
        ),
        pytest.param([10, 20, 5, 30, 15], None, 100.0, [], id="no_deviations_100_percent_threshold"),
        pytest.param([0, 0, 0, 0], None, 50.0, [], id="all_zeros"),
        pytest.param(
            [0, 10, 0, -10, 0],
            None,
            50.0,
            [
                ComparativeInsight(index=1, value=10.0, average=0.0, deviation_percent=float("inf"), description=""),
                ComparativeInsight(index=3, value=-10.0, average=0.0, deviation_percent=float("inf"), description=""),
            ],
            id="zero_average_infinite_deviation",
        ),
        pytest.param(
            [1.0, 1.5, 0.8, 2.0, 1.2],
            None,
            20.0,
            [
                ComparativeInsight(
                    index=0, value=1.0, average=1.3, deviation_percent=23.076923076923077, description=""
                ),
                ComparativeInsight(
                    index=2, value=0.8, average=1.3, deviation_percent=38.46153846153846, description=""
                ),
                ComparativeInsight(
                    index=3, value=2.0, average=1.3, deviation_percent=53.84615384615385, description=""
                ),
            ],
            id="float_values_with_deviation",
        ),
    ],
)
def test_comparison_to_average(data, x_labels, threshold_deviation_percent, expected_insights):
    actual_insights = ComparativeInsights.comparison_to_average(data, x_labels, threshold_deviation_percent)

    actual_insights.sort(key=lambda i: (i.index, i.value))
    expected_insights.sort(key=lambda i: (i.index, i.value))

    assert len(actual_insights) == len(expected_insights)

    for actual, expected in zip(actual_insights, expected_insights):
        assert actual.index == expected.index
        assert actual.value == expected.value
        assert actual.average == expected.average
        if actual.deviation_percent == float("inf"):
            assert expected.deviation_percent == float("inf")
        else:
            assert round(actual.deviation_percent) == round(expected.deviation_percent)
