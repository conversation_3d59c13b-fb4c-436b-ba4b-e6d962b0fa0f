from typing import List, Optional

import numpy as np

from services.data_service.application.use_cases.analyse.models.data_models import (
    ConsistencyInsight,
    ConsistencyLevel,
    TrendInsight,
    TrendSegment,
    TrendType,
)


class PatternRecognition:
    @staticmethod
    def _get_trend_direction(slope: float, data_range: float) -> TrendType:
        if data_range == 0:
            return TrendType.STABLE
        if slope > 0:
            return TrendType.INCREASING
        return TrendType.DECREASING

    @staticmethod
    def _calculate_overall_trend(data: List[float]) -> tuple[float, float, TrendType, float]:
        n = len(data)
        x_overall = np.arange(n)
        overall_slope, overall_intercept = np.polyfit(x_overall, data, 1)
        overall_data_range = np.max(data) - np.min(data)
        overall_trend_type = PatternRecognition._get_trend_direction(overall_slope, overall_data_range)
        relative_overall_slope = abs(overall_slope) / (overall_data_range + 1e-9)
        return overall_slope, overall_intercept, overall_trend_type, relative_overall_slope

    @staticmethod
    def _validate_window_size(window_size: int) -> int:
        if window_size < 3:
            window_size = 3
        if window_size % 2 == 0:
            window_size += 1
        return window_size

    @staticmethod
    def _detect_trend_segments(
        data: List[float],
        window_size: int,
        significant_slope_threshold: float,
        trend_change_persistence_threshold: int,
    ) -> List[TrendSegment]:
        """
        Iterates through data to detect localized trend segments.
        """
        n = len(data)
        trend_segments = []
        current_segment_start_index = 0
        current_dominant_trend_type: Optional[TrendType] = None
        consecutive_different_trend_windows = 0

        for i in range(n - window_size + 1):
            window_data = data[i : i + window_size]
            window_x = np.arange(window_size)
            current_window_slope, _ = np.polyfit(window_x, window_data, 1)

            window_data_range = np.max(window_data) - np.min(window_data)
            current_window_trend_type = PatternRecognition._get_trend_direction(current_window_slope, window_data_range)

            if current_dominant_trend_type is None:
                current_dominant_trend_type = current_window_trend_type
                continue

            if current_window_trend_type != current_dominant_trend_type:
                consecutive_different_trend_windows += 1
                if consecutive_different_trend_windows >= trend_change_persistence_threshold:
                    segment_end_index_for_previous = i - 1 - (consecutive_different_trend_windows - 1)
                    if segment_end_index_for_previous < current_segment_start_index:
                        segment_end_index_for_previous = current_segment_start_index

                    segment_data_to_add = data[current_segment_start_index : segment_end_index_for_previous + 1]

                    if len(segment_data_to_add) >= 3:
                        segment_x = np.arange(len(segment_data_to_add))
                        seg_slope, _ = np.polyfit(segment_x, segment_data_to_add, 1)
                        seg_data_range = np.max(segment_data_to_add) - np.min(segment_data_to_add)
                        seg_trend_type = PatternRecognition._get_trend_direction(seg_slope, seg_data_range)

                        if abs(seg_slope) > significant_slope_threshold:
                            trend_segments.append(
                                TrendSegment(
                                    start_index=current_segment_start_index,
                                    end_index=segment_end_index_for_previous,
                                    trend_type=seg_trend_type,
                                    slope=seg_slope,
                                )
                            )
                    current_segment_start_index = segment_end_index_for_previous + 1
                    current_dominant_trend_type = current_window_trend_type
                    consecutive_different_trend_windows = 0
            else:
                consecutive_different_trend_windows = 0

        # Check and add last segment if valid
        segment_data_for_last_segment = data[current_segment_start_index:n]
        if len(segment_data_for_last_segment) >= 3:
            segment_x_for_last = np.arange(len(segment_data_for_last_segment))
            seg_slope, _ = np.polyfit(segment_x_for_last, segment_data_for_last_segment, 1)
            seg_data_range = np.max(segment_data_for_last_segment) - np.min(segment_data_for_last_segment)
            seg_trend_type = PatternRecognition._get_trend_direction(seg_slope, seg_data_range)
            if abs(seg_slope) > significant_slope_threshold:
                trend_segments.append(
                    TrendSegment(
                        start_index=current_segment_start_index,
                        end_index=n - 1,
                        trend_type=seg_trend_type,
                        slope=seg_slope,
                    )
                )
        return trend_segments

    @staticmethod
    def _generate_description(
        overall_trend_type: TrendType,
        data: List[float],
        x_labels: Optional[List[str]],
        trend_segments: List[TrendSegment],
        overall_slope: float,
    ) -> str:
        description_parts = []
        overall_description = (
            f"Overall, the data shows {'an' if overall_slope > 0 else 'a'} {overall_trend_type.value.lower()} trend."
        )
        description_parts.append(overall_description)

        if trend_segments:
            for segment in trend_segments:
                start_label = x_labels[segment.start_index] if x_labels else f"index {segment.start_index}"
                end_label = x_labels[segment.end_index] if x_labels else f"index {segment.end_index}"
                description_parts.append(
                    f"From {start_label} to {end_label}, there's {'an' if segment.slope > 0 else 'a'} "
                    f"trend with slope: {segment.slope:.2f}."
                )
            return " ".join(description_parts)
        else:
            if x_labels:
                overall_description += (
                    f" It ranges from approximately {data[0]:.1f} at {x_labels[0]} to {data[-1]:.1f} at {x_labels[-1]}."
                )
            else:
                overall_description += (
                    f" It ranges from approximately {data[0]:.1f} at the start to {data[-1]:.1f} at the end."
                )
            return overall_description

    @staticmethod
    def trend_analysis_dynamic(
        data: List[float],
        x_labels: Optional[List[str]] = None,
        window_size: int = 5,
        significant_slope_threshold: float = 0.1,
        trend_change_persistence_threshold: int = 2,
    ) -> TrendInsight:
        """
        Detects and describes overall and localized trends in the data.
        """
        n = len(data)

        window_size = PatternRecognition._validate_window_size(window_size=window_size)

        if n < 3:
            return TrendInsight(
                overall_trend=TrendType.STABLE,
                description="Not enough data to perform trend analysis. Need at least 3 data points.",
                slope=None,
                intercept=None,
                start_label=x_labels[0] if x_labels else None,
                end_label=x_labels[-1] if x_labels else None,
            )

        overall_slope, overall_intercept, overall_trend_type, relative_overall_slope = (
            PatternRecognition._calculate_overall_trend(data)
        )

        if n < window_size:
            description = (
                f"Not enough data for dynamic trend analysis with window size {window_size}. "
                f"Overall trend is {overall_trend_type.value.lower()}."
            )
            return TrendInsight(
                overall_trend=overall_trend_type,
                description=description,
                slope=overall_slope,
                relative_slope=relative_overall_slope,
                intercept=overall_intercept,
                start_label=x_labels[0] if x_labels else None,
                end_label=x_labels[-1] if x_labels else None,
            )

        trend_segments = PatternRecognition._detect_trend_segments(
            data, window_size, significant_slope_threshold, trend_change_persistence_threshold
        )

        description = PatternRecognition._generate_description(
            overall_trend_type, data, x_labels, trend_segments, overall_slope
        )

        return TrendInsight(
            overall_trend=overall_trend_type,
            description=description,
            relative_slope=relative_overall_slope,
            slope=overall_slope,
            intercept=overall_intercept,
            start_label=x_labels[0] if x_labels else None,
            end_label=x_labels[-1] if x_labels else None,
            trend_segments=trend_segments,
        )

    @staticmethod
    def consistency_check(data: list[float], x_labels: Optional[list[str]] = None) -> ConsistencyInsight:
        data_np = np.array(data)
        if not data_np.size:
            return ConsistencyInsight(
                level=ConsistencyLevel.INCONSISTENT, description="No data provided for consistency check."
            )

        std_dev = np.std(data_np)
        mean_val = np.mean(data_np)

        level = ConsistencyLevel.INCONSISTENT
        description = ""

        if mean_val == 0:
            if std_dev == 0:
                level = ConsistencyLevel.PERFECTLY_UNIFORM
                description = "Your data is perfectly uniform with all values being zero."
            else:
                level = ConsistencyLevel.INCONSISTENT
                description = "Data is zero-centric with some variations."
        else:
            coefficient_of_variation = std_dev / mean_val

            if coefficient_of_variation < 0.01:  # Very low variation
                level = ConsistencyLevel.PERFECTLY_UNIFORM
                if len(set(data)) == 1:
                    description = f"You have a perfectly uniform data, with all values being {data[0]:.1f}."
                else:
                    description = f"Your data is perfectly uniform, with negligible variation around {mean_val:.1f}."
            elif coefficient_of_variation < 0.05:  # Low variation
                level = ConsistencyLevel.HIGHLY_CONSISTENT
                description = f"You maintain a highly consistent pattern, with values hovering around {mean_val:.1f} across the {'days' if x_labels and len(x_labels) == 7 else 'months' if x_labels and len(x_labels) == 12 else 'series'}."
            elif coefficient_of_variation < 0.15:  # Moderate variation
                level = ConsistencyLevel.MODERATELY_CONSISTENT
                description = (
                    f"Your data shows moderate consistency, with an average of {mean_val:.1f} and some variations."
                )
            else:
                level = ConsistencyLevel.INCONSISTENT
                description = "Your data shows significant variability and is not highly consistent."

        return ConsistencyInsight(
            level=level, description=description, mean_value=float(mean_val), std_dev=float(std_dev)
        )
