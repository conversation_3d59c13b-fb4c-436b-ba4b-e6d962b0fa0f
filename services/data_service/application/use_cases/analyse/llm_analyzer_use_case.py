import logging
from typing import List, Optional

from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.data_service.application.use_cases.analyse.llm_analyser_prompt import llm_analyser_prompt
from services.data_service.application.use_cases.analyse.models.data_models import DataOrigin, LLMInsight


class LLMAnalyzerUseCase:
    def __init__(self, model: Model):
        self._model = model

    async def get_insights(
        self,
        data: List[float],
        x_labels: Optional[List[str]] = None,
        data_origin_description: Optional[DataOrigin] = None,
        data_series_description: Optional[str] = None,
    ) -> Optional[LLMInsight]:
        """
        Generates structured insights from the LLM by analyzing the raw data series directly.
        """
        analyzer_agent = Agent(model=self._model, system_prompt=llm_analyser_prompt)
        if not data:
            logging.info("No data provided to LLMAnalyzer for direct analysis.")
            return None

        prompt_components = [
            "Please analyze the following data series:",
            f"Data Series: {data}",
        ]

        if x_labels:
            prompt_components.append(f"X-axis Labels (if applicable): {x_labels}")
            prompt_components.append(
                "Consider the labels as context for the corresponding data points (e.g., dates, categories like weekdays)."
            )
        else:
            prompt_components.append(
                "No specific x-axis labels are provided, treat this as a generic numerical sequence."
            )

        if data_origin_description:
            prompt_components.append(f"Data Origin Context: {data_origin_description}")

        if data_series_description:
            prompt_components.append(f"Data Series Represents: {data_series_description}")

        full_prompt = "\n".join(prompt_components)

        try:
            llm_response = await analyzer_agent.run(full_prompt, output_type=LLMInsight)
            llm_insights: LLMInsight = llm_response.output  # pyright: ignore
            llm_insights.disclaimer = "These insights are generated by an AI and are for informational purposes only. They are not intended to be medical advice, diagnosis, or treatment. Always consult with a qualified healthcare professional for any health concerns or before making any decisions related to your health."
            return llm_insights
        except Exception as e:
            logging.error(f"Error calling LLM: {e}")
            return None
