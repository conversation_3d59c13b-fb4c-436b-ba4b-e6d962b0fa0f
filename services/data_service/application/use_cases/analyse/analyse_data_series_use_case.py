from typing import List, Optional

from pydantic import Field

from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.analyse.analyse_helpers.anomaly_detection import AnomalyDetection
from services.data_service.application.use_cases.analyse.analyse_helpers.comparative_insights import ComparativeInsights
from services.data_service.application.use_cases.analyse.analyse_helpers.pattern_recognition import PatternRecognition
from services.data_service.application.use_cases.analyse.llm_analyzer_use_case import LLMAnalyzerUseCase
from services.data_service.application.use_cases.analyse.models.data_models import (
    Anomaly,
    ComparativeInsight,
    ConsistencyInsight,
    DataOrigin,
    ExtremeValueInsight,
    LLMInsight,
    TrendInsight,
)


class AnalyseDataSeriesUseCaseInputBoundary(BaseDataModel):
    data_series: List[float] = Field(min_length=2)
    x_labels: Optional[List[str]] = Field()
    data_origin_description: Optional[DataOrigin] = Field()
    data_series_description: Optional[str] = Field()
    should_call_llm: bool = Field(default=False)


class AnalyseDataSeriesUseCaseOutputBoundary(BaseDataModel):
    anomalies: List[Anomaly] = Field()
    trend_insight: TrendInsight = Field()
    consistency_insight: ConsistencyInsight = Field()
    extreme_value_insight: ExtremeValueInsight = Field()
    comparative_insights: List[ComparativeInsight] = Field()
    llm_insight: Optional[LLMInsight] = Field()


class AnalyseDataSeriesUseCase:
    def __init__(
        self,
        llm_analyzer_use_case: LLMAnalyzerUseCase,
    ):
        self.llm_analyzer_use_case = llm_analyzer_use_case

    async def execute_async(
        self, input_boundary: AnalyseDataSeriesUseCaseInputBoundary
    ) -> AnalyseDataSeriesUseCaseOutputBoundary:
        anomalies = AnomalyDetection.iqr_detect(
            data_series=input_boundary.data_series, x_labels=input_boundary.x_labels
        )

        trend_insight = PatternRecognition.trend_analysis_dynamic(
            data=input_boundary.data_series, x_labels=input_boundary.x_labels
        )

        consistency_insight = PatternRecognition.consistency_check(
            data=input_boundary.data_series, x_labels=input_boundary.x_labels
        )

        extreme_value_insight = ComparativeInsights.identify_extremes(
            data=input_boundary.data_series, x_labels=input_boundary.x_labels
        )

        comparative_insights = ComparativeInsights.comparison_to_average(
            data=input_boundary.data_series, x_labels=input_boundary.x_labels
        )
        llm_insight = None
        if self.llm_analyzer_use_case and input_boundary.should_call_llm:
            llm_insight = await self.llm_analyzer_use_case.get_insights(
                data=input_boundary.data_series,
                x_labels=input_boundary.x_labels,
                data_origin_description=input_boundary.data_origin_description,
                data_series_description=input_boundary.data_series_description,
            )

        return AnalyseDataSeriesUseCaseOutputBoundary(
            anomalies=anomalies,
            trend_insight=trend_insight,
            consistency_insight=consistency_insight,
            extreme_value_insight=extreme_value_insight,
            comparative_insights=comparative_insights,
            llm_insight=llm_insight,
        )
