import pytest

from services.data_service.application.use_cases.analyse.llm_analyzer_use_case import (
    Data<PERSON>rigin,
    InsightSeverity,
    LLMAnalyzerUseCase,
    LLMInsight,
)


@pytest.mark.integration
async def test_get_insights_simple_data(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)

    data = [10.0, 12.0, 11.0, 13.0, 10.5]
    x_labels = ["Day 1", "Day 2", "Day 3", "Day 4", "Day 5"]
    data_origin_description = DataOrigin.Other
    data_series_description = "Daily temperature readings"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)


@pytest.mark.integration
async def test_get_insights_simple_calendar_aggregation_data(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)

    data = [12, 1, 2, 1, 0, 2, 1]
    x_labels = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
    data_origin_description = DataOrigin.CalendarAggregation
    data_series_description = "Data Series Variable: Headaches count"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)
    assert actual_insight.insights.severity in [InsightSeverity.MEDIUM, InsightSeverity.HIGH]


@pytest.mark.integration
async def test_get_insights_simple_date_histogram_data(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)

    data = [7000, 8000, 9000, 10000, 11000]
    x_labels = ["1.6.2025", "8.6.2025", "15.6.2025", "22.6.2025", "29.6.2025"]
    data_origin_description = DataOrigin.HistogramAggregation
    data_series_description = "Weekly aggregated average steps per day"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)


@pytest.mark.integration
async def test_get_insights_high_severity_anomaly(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)
    data = [70, 72, 68, 71, 105, 73, 69]
    x_labels = ["Day 1", "Day 2", "Day 3", "Day 4", "Day 5", "Day 6", "Day 7"]
    data_origin_description = DataOrigin.CalendarAggregation
    data_series_description = "Daily resting heart rate (bpm)"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)
    assert actual_insight.insights.severity in [InsightSeverity.MEDIUM, InsightSeverity.HIGH]


@pytest.mark.integration
async def test_get_insights_downward_trend(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)
    data = [120, 115, 110, 105, 100]
    x_labels = ["Week 1", "Week 2", "Week 3", "Week 4", "Week 5"]
    data_origin_description = DataOrigin.CalendarAggregation
    data_series_description = "Weekly average blood pressure (systolic)"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)
    assert actual_insight.insights.severity in [InsightSeverity.MEDIUM, InsightSeverity.HIGH]


@pytest.mark.integration
async def test_get_insights_unremarkable_data(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)
    data = [7.5, 7.8, 7.6, 7.7, 7.9, 7.6, 7.8]
    x_labels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
    data_origin_description = DataOrigin.CalendarAggregation
    data_series_description = "Daily average sleep duration (hours)"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)
    assert actual_insight.insights.severity == InsightSeverity.VERY_LOW


@pytest.mark.integration
async def test_get_insights_frequency_distribution_data(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)
    data = [5, 1, 8, 2]
    x_labels = ["Morning", "Afternoon", "Evening", "Night"]
    data_origin_description = DataOrigin.FrequencyDistribution
    data_series_description = "Frequency of energy dips"

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=x_labels,
        data_origin_description=data_origin_description,
        data_series_description=data_series_description,
    )

    assert isinstance(actual_insight.insights, LLMInsight)
    assert actual_insight.insights.severity in [InsightSeverity.MEDIUM, InsightSeverity.HIGH]


async def test_get_insights_empty_data(dependency_bootstrapper):
    analyzer = dependency_bootstrapper.get(LLMAnalyzerUseCase)
    data = []

    actual_insight = await analyzer.get_insights(
        data,
        x_labels=None,
        data_origin_description=DataOrigin.Other,
        data_series_description="Empty data series",
    )

    assert actual_insight.insights is None
