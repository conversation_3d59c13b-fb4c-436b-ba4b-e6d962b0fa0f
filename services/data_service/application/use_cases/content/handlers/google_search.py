from urllib.parse import parse_qs, urlparse

from httpx import AsyncClient

from services.base.application.exceptions import NoContentException
from services.base.domain.schemas.events.activity import ActivityCategory
from services.data_service.application.use_cases.content.content_lookup_boundaries import ContentLookupOutputBoundary
from services.data_service.application.use_cases.content.handlers.content_handler_base import ContentHandlerBase


class GoogleSearchHandler(ContentHandlerBase):
    """Handler for Google search URLs."""

    def __init__(self, client: AsyncClient):
        self._client = client

    async def can_handle(self, url: str) -> bool:
        parsed_url = urlparse(url)
        can_handle = parsed_url.netloc.replace("www.", "") == "google.com" and parsed_url.path == "/search"
        return can_handle

    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        try:
            # The URL is already the final URL after redirects from the use case
            response = await self._client.get(
                url=url,
                timeout=ContentHandlerBase._TIMEOUT,
                follow_redirects=False,
                headers=ContentHandlerBase._BROWSER_HEADERS,
            )
            response.raise_for_status()

            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)

            if "q" not in query_params:
                raise NoContentException("No search query found in URL")

            search_query = query_params["q"][0]

            return self._normalize_output(
                title=f"Google Search: {search_query}",
                category=ActivityCategory.APP_OTHER,
                description=f"Search results for: {search_query}",
            )

        except Exception as e:
            raise NoContentException(f"Unable to process Google search URL: {str(e)}")
