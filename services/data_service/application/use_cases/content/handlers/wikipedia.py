import re
from urllib.parse import urlparse

from httpx import AsyncClient
from scrapy import Selector

from services.base.application.exceptions import NoContentException
from services.base.domain.schemas.events.content.content import ContentCategory
from services.data_service.application.use_cases.content.content_lookup_boundaries import ContentLookupOutputBoundary
from services.data_service.application.use_cases.content.handlers.content_handler_base import ContentHandlerBase


class WikipediaHandler(ContentHandlerBase):
    """Handler for Wikipedia content."""

    def __init__(self, client: AsyncClient):
        self._client = client

    async def can_handle(self, url: str) -> bool:
        parsed_url = urlparse(url)
        return parsed_url.netloc.endswith("wikipedia.org")

    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        try:
            response = await self._client.get(
                url=url,
                timeout=ContentHandlerBase._TIMEOUT,
                follow_redirects=True,
                headers=ContentHandlerBase._BROWSER_HEADERS,
            )
            response.raise_for_status()
        except Exception as e:
            raise NoContentException(f"Unable to retrieve Wikipedia content: {str(e)}")

        selector = Selector(text=response.text)

        # Get title from meta tags or title tag, and clean up the "- Wikipedia" suffix
        title = selector.css('meta[property="og:title"]::attr(content)').get() or selector.css("title::text").get()
        if not title:
            raise NoContentException(f"Could not parse Wikipedia title from {url}")

        # Remove "- Wikipedia" suffix if present
        if title.endswith("- Wikipedia"):
            title = title[:-11].strip()

        # 1. selector.css(...) – Selects elements from the page using CSS selectors.
        # 2. #mw-content-text – Targets the main content area of a Wikipedia article.
        # 3. > .mw-parser-output > p – Selects <p> elements inside .mw-parser-output, which contains the main article text.
        # 4. :not(.mw-empty-elt) – Excludes empty paragraphs.
        # 5. :not([class*='hatnote']) – Excludes hatnote paragraphs.
        # 6. :not([class*='coordinates']) – Excludes coordinate paragraphs.
        description_selector = selector.css(
            "#mw-content-text > .mw-parser-output > p:not(.mw-empty-elt):not([class*='hatnote']):not([class*='coordinates'])"
        )

        # 1. normalize-space(string()) – Normalizes whitespace and removes empty text nodes.
        # 2. get() – Returns the first matching element's text content.
        description = description_selector.xpath("normalize-space(string())").get()

        if description:
            # Remove reference numbers [1], [2] and letters [a], [b] etc.
            description = re.sub(r"\[\w+\]", "", description).strip()

        return self._normalize_output(
            title=title.strip(),
            category=ContentCategory.ARTICLE,
            description=description.strip() if description else None,
        )
