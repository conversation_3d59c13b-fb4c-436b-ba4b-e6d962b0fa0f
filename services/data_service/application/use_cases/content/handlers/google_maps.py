"""Handler for Google Maps URLs."""

import re
from urllib.parse import unquote, urlparse

from httpx import AsyncClient

from services.base.application.exceptions import NoContentException
from services.base.domain.schemas.events.activity import ActivityCategory
from services.data_service.application.use_cases.content.content_lookup_boundaries import ContentLookupOutputBoundary
from services.data_service.application.use_cases.content.handlers.content_handler_base import ContentHandlerBase


class GoogleMapsHandler(ContentHandlerBase):
    # Mobile User-Agent forces simpler URL redirects
    _USER_AGENT = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1"
    _COORDS_PATTERN = re.compile(r"/@.*$")

    def __init__(self, client: AsyncClient):
        self._client = client

    async def can_handle(self, url: str) -> bool:
        """Check if the URL is a Google Maps URL with a place location."""
        parsed = urlparse(url)
        netloc = parsed.netloc.replace("www.", "").lower()
        path = parsed.path.rstrip("/")

        # Short URLs always redirect to place URLs
        if netloc == "maps.app.goo.gl":
            return True

        # All other domains require /place/ in the path
        if netloc not in {"maps.google.com", "google.com"}:
            return False

        # google.com requires /maps/place/, maps.google.com only needs /place/
        required_path = "/maps/place/" if netloc == "google.com" else "/place/"
        return required_path in path

    async def handle(self, url: str) -> ContentLookupOutputBoundary:
        """Fetch and process a Google Maps URL to extract location details."""
        try:
            response = await self._client.get(url, headers={"User-Agent": self._USER_AGENT}, follow_redirects=True)
            location = self._extract_location_from_url(str(response.url))
            return self._normalize_output(
                title=location,
                category=ActivityCategory.APP_OTHER,
                description=f"Location details for: {location}",
            )
        except Exception as e:
            raise NoContentException(f"Failed to fetch Google Maps URL: {str(e)}")

    def _extract_location_from_url(self, url: str) -> str:
        """Extract the location name from a Google Maps URL."""
        try:
            segments = [s for s in urlparse(url).path.split("/") if s]
            place_index = segments.index("place")

            # Check if there's a location after 'place'
            if place_index + 1 >= len(segments):
                raise NoContentException("Empty location name")

            raw_location = segments[place_index + 1]
            if not raw_location or raw_location.isspace():
                raise NoContentException("Empty location name")

            if raw_location.startswith("@"):
                raise NoContentException("URL contains only coordinates")

            clean_location = unquote(self._COORDS_PATTERN.sub("", raw_location).replace("+", " "))
            if not clean_location or clean_location.isspace():
                raise NoContentException("Empty location name")

            return clean_location
        except (ValueError, IndexError):
            raise NoContentException("Could not extract location from URL")
