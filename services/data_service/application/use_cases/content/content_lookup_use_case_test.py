import pytest
from pydantic import HttpUrl

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.activity import ActivityCategory
from services.base.domain.schemas.events.content.content import ContentCategory
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary
from services.data_service.application.use_cases.content.content_lookup_use_case import (
    ContentLookupOutputBoundary,
    ContentLookupUseCase,
)


class TestContentLookupUseCase:

    content_lookup_test_cases = [
        pytest.param(
            ContentLookupInputBoundary(
                url=HttpUrl(
                    "https://www.google.com/maps/place/Tesla+HQ/@37.3940974,-122.1527721,797m/data=!3m2!1e3!4b1!4m6!3m5!1s0x808fb075776f1c3b:0xccc17e4da6b38370!8m2!3d37.3940932!4d-122.1501918!16s%2Fg%2F1tcxvgyb?entry=ttu&g_ep=EgoyMDI1MDMwMy4wIKXMDSoASAFQAw%3D%3D"
                )
            ),
            ContentLookupOutputBoundary(
                title="Tesla HQ",
                category=ActivityCategory.APP_OTHER,
                description="Location details for: Tesla HQ",
                type=DataType.Activity,
            ),
            id="google_maps_tesla_hq",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://maps.app.goo.gl/3mywMiKMBMztM21g7")),
            ContentLookupOutputBoundary(
                title="Angels Landing",
                category=ActivityCategory.APP_OTHER,
                description="Location details for: Angels Landing",
                type=DataType.Activity,
            ),
            id="google_maps_angels_landing",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://www.youtube.com/watch?v=Nb2tebYAaOA")),
            ContentLookupOutputBoundary(
                title="Jim Keller: Moore's Law, Microprocessors, and First Principles | Lex Fridman Podcast #70",
                category=ContentCategory.CONTENT,
                description="placeholder",
                type=DataType.Content,
            ),
            id="youtube_lex_fridman_jim_keller",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://youtu.be/ugvHCXCOmm4?si=ydlAxSnq-LsNoTzK")),
            ContentLookupOutputBoundary(
                title="Dario Amodei: Anthropic CEO on Claude, AGI & the Future of AI & Humanity | Lex Fridman Podcast #452",
                category=ContentCategory.CONTENT,
                description="placeholder",
                type=DataType.Content,
            ),
            id="youtube_lex_fridman_dario_amodei",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://www.youtube.com/shorts/n-7bF9TwNdw")),
            ContentLookupOutputBoundary(
                title="Exploring & Hunting Fish For DINNER!! ( Credit : @OutdoorBoys )",
                category=ContentCategory.CONTENT,
                description=None,
                type=DataType.Content,
            ),
            id="youtube_shorts_outdoor_boys",
        ),
        # TODO: SO added bot protection that requires JS / Cookies support, scraping it would require a headless browser or the like
        # pytest.param(
        #     ContentLookupInputBoundary(url=HttpUrl("https://stackoverflow.com/questions/11828270/how-do-i-exit-vim")),
        #     ContentLookupOutputBoundary(
        #         title="How do I exit Vim?",
        #         type=ContentCategory.CONTENT,
        #         description="I am stuck and cannot escape. It says:\ntype :quit&lt;Enter&gt; to quit VIM\n\nBut when I type that it simply appears in the object body.",
        #     ),
        #     id="stackoverflow_exit_vim",
        # ),
        pytest.param(
            ContentLookupInputBoundary(
                url=HttpUrl("https://medium.com/a-journey-with-go/go-overview-of-the-compiler-4e5a153ca889")
            ),
            ContentLookupOutputBoundary(
                title="Go: Overview of the Compiler",
                category=ContentCategory.ARTICLE,
                description="ℹ️ This article is based on Go 1.13.",
                type=DataType.Content,
            ),
            id="medium_go_compiler",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://news.ycombinator.com")),
            ContentLookupOutputBoundary(
                title="Hacker News",
                category=ContentCategory.CONTENT,
                description=None,
                type=DataType.Content,
            ),
            id="hacker_news",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://www.youtube.com/watch?v=dQw4w9WgXcQ")),
            ContentLookupOutputBoundary(
                title="Rick Astley - Never Gonna Give You Up (Official Video) (4K Remaster)",
                category=ContentCategory.CONTENT,
                description="placeholder",
                type=DataType.Content,
            ),
            id="youtube_rick_astley",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://www.instagram.com/share/reel/_jgXF2Mm6")),
            ContentLookupOutputBoundary(
                title='Entrepreneurs On IG on Instagram: "Jeff Bezos has shared some practical wisdom about stress that many people find relatable.',
                category=ContentCategory.ARTICLE,
                description="placeholder",
                type=DataType.Content,
            ),
            id="instagram_jeff_bezos",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://x.com/karpathy/status/1885026028428681698")),
            ContentLookupOutputBoundary(
                title="Post by Andrej Karpathy (@karpathy)",
                category=ContentCategory.CONTENT,
                description="placeholder",
                type=DataType.Content,
            ),
            id="x_andrej_karpathy",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://x.com/French_Jim/status/1496221240431988740")),
            ContentLookupOutputBoundary(
                title="Post by Jim French (@French_Jim)",
                category=ContentCategory.CONTENT,
                description="On February 22 2022, the Live Learn Innovate Foundation LLIF is introducing Best Life App to help you achieve your best life. Your digital life starts today!",
                type=DataType.Content,
            ),
            id="x_jim_french",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://x.com/CT24zive/status/1896842234894655733")),
            ContentLookupOutputBoundary(
                title="Post by ČT24 (@CT24zive)",
                category=ContentCategory.CONTENT,
                description="USA zvažují, že omezí sankce namířené proti Rusku i sdílení zpravodajských informací s Ukrajinou, uvedly agentura Reuters a stanice CNN.",
                type=DataType.Content,
            ),
            id="x_ct24_news",
        ),
        pytest.param(
            ContentLookupInputBoundary(url=HttpUrl("https://g.co/kgs/NG7mqZN")),
            ContentLookupOutputBoundary(
                title="Google Search: Angels Landing",
                category=ActivityCategory.APP_OTHER,
                description="Search results for: Angels Landing",
                type=DataType.Activity,
            ),
            id="google_search_angels_landing",
        ),
    ]

    @pytest.mark.integration
    @pytest.mark.parametrize("input, expected_output", content_lookup_test_cases)
    async def test_content_lookup_use_case_returns_data(
        self,
        input: ContentLookupInputBoundary,
        content_lookup_use_case: ContentLookupUseCase,
        expected_output: ContentLookupOutputBoundary,
    ):
        result = await content_lookup_use_case.execute(input_boundary=input)
        assert result.title == expected_output.title
        assert result.category == expected_output.category

        if expected_output.description is None:
            assert result.description is None, "Expected no description"
        # We expect description to be filled but changing
        elif expected_output.description == "placeholder":
            assert result.description, "Expected non-empty description"
        else:
            assert expected_output.description == result.description
