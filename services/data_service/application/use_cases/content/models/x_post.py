from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.schemas.events.content.content import ContentValueLimits
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.shared import BaseDataModel


class XPost(BaseDataModel):
    """Model representing an X (formerly Twitter) post's content."""

    title: NonEmptyStr = Field(
        description="Author information in the format: 'Post by {name} (@username)'",
        max_length=ContentValueLimits.TITLE_MAX_LENGTH,
    )
    description: NonEmptyStr | None = Field(
        description="Content of the X post, truncated if exceeds length limit",
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
