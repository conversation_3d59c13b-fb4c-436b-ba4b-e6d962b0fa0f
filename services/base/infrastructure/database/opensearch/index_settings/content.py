from typing import Any, Dict

from opensearchpy import Integer, Keyword, Text

from services.base.domain.schemas.events.content.content import ContentFields, ContentValueLimits
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_event_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    CONTENT_INDEX,
    OpenSearchIndex,
)

content_mapping = {
    ContentFields.TITLE: Text(
        fields={"keyword": Keyword(ignore_above=ContentValueLimits.TITLE_MAX_LENGTH)}, copy_to=OS_LABEL_CATCH_ALL
    ),
    ContentFields.URL: Text(copy_to=OS_LABEL_CATCH_ALL),
    ContentFields.RATING: Integer(),
}


def get_content_mapping() -> Dict[str, Any]:

    return convert_dsl_mapping_to_dict(custom_mapping=content_mapping | get_base_event_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": CONTENT_INDEX,
    }


ContentIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=CONTENT_INDEX,
    mappings=get_content_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[CONTENT_INDEX],
)
