import asyncio
import logging

from opensearchpy import AsyncOpenSearch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def run_migration(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    template_repo = bootstrapper.get(interface=TemplateRepository)
    index_name = TemplateIndexModel.name

    query_to_migrate = {"term": {"document.type": {"value": "interactive"}}}
    count_to_migrate = (await client.count(index=index_name, body={"query": query_to_migrate}))["count"]

    if dry_run:
        logger.info(f"[DRY RUN] would update {count_to_migrate} content documents")
        return

    body = {
        "script": {
            "source": """
                ctx._source.document.type = "activity";
                ctx._source.document_type = "activity";
                
                def cat = ctx._source.document.category;
                
                String note = ctx._source.document.containsKey('note') && ctx._source.document.note != null ? ctx._source.document.note : "";
                String titlePart = ctx._source.document.containsKey('title') && ctx._source.document.title != null ? "Title: " + ctx._source.document.title : "";
                String urlPart = ctx._source.document.containsKey('url') && ctx._source.document.url != null ? "URL: " + ctx._source.document.url : "";
                
            
                if (!titlePart.isEmpty() && !urlPart.isEmpty()) {
                    note += "<div>" + titlePart + "</div>" + "<div>" + urlPart + "</div>"
                } else if (!titlePart.isEmpty()) {
                    note += "<div>" + titlePart + "</div>";
                } else if (!urlPart.isEmpty()) {
                    note += "<div>" + urlPart + "</div>";
                }
                
                if (!note.isEmpty()) {
                    ctx._source.document.note = note;
                }
                
                
                ctx._source.document.remove('title');
                ctx._source.document.remove('url');
                
                if (cat == 'interactive') {   
                    ctx._source.document.category = 'interactive_other';
                } else if (cat == 'app') {
                    ctx._source.document.category = 'app_other';
                } else if (cat == 'game') {
                    ctx._source.document.category = 'interactive_other';
                } else if (cat == 'virtual_reality') {
                    ctx._source.document.category = 'app_interactive_virtual_reality';
                } else if (cat == 'augmented_reality') {
                    ctx._source.document.category = 'app_interactive_augmented_reality';
                } else if (cat == 'board_game') {
                    ctx._source.document.category = 'interactive_board_game';
                } else if (cat == 'card_game') {
                    ctx._source.document.category = 'interactive_card_game';
                } else if (cat == 'puzzle') {
                    ctx._source.document.category = 'interactive_puzzle';
                } else if (cat == 'quiz') {
                    ctx._source.document.category = 'interactive_other';
                } else {
                    ctx._source.document.category = 'other';
                }
            """,
            "lang": "painless",
        },
        "query": query_to_migrate,
    }
    await update_by_query_async(client=client, data_type=DataType.EventTemplate, query=body)
    count_after_migration = (await client.count(index=index_name, body={"query": query_to_migrate}))["count"]
    assert count_after_migration == 0

    query = Query(
        type_queries=[
            TypeQuery(domain_types=[EventTemplate], query=ValuesQuery(field_name="document_type", values=["activity"]))
        ]
    )
    # checking whether all templates are serializable
    await template_repo.search_by_query(query=query, size=10000)
    await client.close()


if __name__ == "__main__":
    asyncio.run(run_migration(dry_run=True))
