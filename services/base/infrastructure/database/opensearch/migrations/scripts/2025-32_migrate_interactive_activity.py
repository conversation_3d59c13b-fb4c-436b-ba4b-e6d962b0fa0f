import asyncio
import logging

from opensearchpy import AsyncOpenSearch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.infrastructure.database.opensearch.index_settings.activity import ActivityIndexModel
from services.base.infrastructure.database.opensearch.index_settings.content import ContentIndexModel
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def run_migration(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    index_name = ContentIndexModel.name
    target_index = ActivityIndexModel

    query = {
        "bool": {
            "filter": [{"terms": {"type": ["interactive"]}}],
        }
    }
    orig_content_count = (await client.count(index=index_name))["count"]
    orig_activity_count = (await client.count(index=target_index.name))["count"]
    count_to_migrate = (await client.count(index=index_name, body={"query": query}))["count"]

    logger.info(f"Original content count: {orig_content_count}")
    logger.info(f"Original activity count: {orig_activity_count}")
    logger.info(f"Content count to migrate: {count_to_migrate}")

    logger.info(f"expected documents: {orig_activity_count}, documents to migrate: {count_to_migrate}")

    script = {
        "source": """
                ctx._source.type = "activity";

                def cat = ctx._source.category;
                
                String note = ctx._source.containsKey('note') && ctx._source.note != null ? ctx._source.note : "";
                String titlePart = ctx._source.containsKey('title') && ctx._source.title != null ? "Title: " + ctx._source.title : "";
                String urlPart = ctx._source.containsKey('url') && ctx._source.url != null ? "URL: " + ctx._source.url : "";
                
            
                if (!titlePart.isEmpty() && !urlPart.isEmpty()) {
                    note += "<div>" + titlePart + "</div>" + "<div>" + urlPart + "</div>"
                } else if (!titlePart.isEmpty()) {
                    note += "<div>" + titlePart + "</div>";
                } else if (!urlPart.isEmpty()) {
                    note += "<div>" + urlPart + "</div>";
                }
                
                if (!note.isEmpty()) {
                    ctx._source.note = note;
                }
                
                ctx._source.remove('title');
                ctx._source.remove('url');
                

                if (cat == 'interactive') {   
                    ctx._source.category = 'interactive_other';
                } else if (cat == 'app') {
                    ctx._source.category = 'app_other';
                } else if (cat == 'game') {
                    ctx._source.category = 'interactive_other';
                } else if (cat == 'virtual_reality') {
                    ctx._source.category = 'app_interactive_virtual_reality';
                } else if (cat == 'augmented_reality') {
                    ctx._source.category = 'app_interactive_augmented_reality';
                } else if (cat == 'board_game') {
                    ctx._source.category = 'interactive_board_game';
                } else if (cat == 'card_game') {
                    ctx._source.category = 'interactive_card_game';
                } else if (cat == 'puzzle') {
                    ctx._source.category = 'interactive_puzzle';
                } else if (cat == 'quiz') {
                    ctx._source.category = 'interactive_other';
                } else {
                    ctx._source.category = 'other';
                }
            """,
        "lang": "painless",
    }
    if dry_run:
        dummy_index = f"dummy-{index_name}"
        await OSMigrationWrapper._re_create_index_with_mappings(
            index_model=target_index,
            target_index_name=dummy_index,
            client=client,
        )
        await OSMigrationWrapper.reindex_index_to(
            source=index_name,
            destination=dummy_index,
            client=client,
            script=script,
            pipeline=None,
            query=query,
        )

        new_count = (await client.count(index=dummy_index))["count"]

        logger.info(f"[DRY RUN] reindexed {new_count} documents from {index_name} to {dummy_index}\n")
        logger.info("-------------\n")
        logger.info(f"COUNT_DIFF: {count_to_migrate - new_count}")
        assert count_to_migrate == new_count

        result = await client.indices.delete(index=dummy_index)
        logger.info(f"[DRY RUN] removed index {dummy_index} with result: {result}")
        return

    await OSMigrationWrapper.reindex_index_to(
        client=client,
        source=f"{index_name}*",
        destination=target_index.name,
        script=script,
        query=query,
        pipeline=target_index.pipeline,
    )
    new_activity_count = (await client.count(index=target_index.name))["count"]
    logging.info(f"new activity documents: {new_activity_count}, diff {new_activity_count - orig_activity_count}")
    assert new_activity_count - orig_activity_count == count_to_migrate

    await client.delete_by_query(index=index_name, body={"query": query}, refresh=True)  # pyright: ignore
    new_content_count = (await client.count(index=index_name))["count"]
    logging.info(f"new content documents: {new_content_count}, diff {orig_content_count - new_content_count}")
    assert orig_content_count - new_content_count == count_to_migrate
    await client.close()


if __name__ == "__main__":
    asyncio.run(run_migration(dry_run=True))
