import asyncio
import logging

from opensearchpy import AsyncOpenSearch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.infrastructure.database.opensearch.index_settings.template import TemplateIndexModel
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import update_by_query_async

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def run_migration(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    template_repo = bootstrapper.get(interface=TemplateRepository)
    index_name = TemplateIndexModel.name

    query_to_migrate = {"terms": {"document.type": ["audio", "interactive", "text", "video", "image"]}}
    query_content = {"term": {"document.type": {"value": "content"}}}
    count_to_migrate = (await client.count(index=index_name, body={"query": query_to_migrate}))["count"]
    origin_content_count = (await client.count(index=index_name, body={"query": query_content}))["count"]

    if dry_run:
        logger.info(f"[DRY RUN] would update {count_to_migrate} content documents")
        return

    body = {
        "script": {
            "source": """
                ctx._source.document.type = "content";
                ctx._source.document_type = "content";

                def cat = ctx._source.document.category;

                if (cat == 'audio') {
                    ctx._source.document.category = 'music';
                } else if (cat == 'podcast') {
                    ctx._source.document.category = 'podcast';
                } else if (cat == 'audiobook') {
                    ctx._source.document.category = 'audiobook';
                } else if (cat == 'music') {
                    ctx._source.document.category = 'music';
                } else if (cat == 'image') {
                    ctx._source.document.category = 'content';
                } else if (cat == 'text' || cat == 'blog' || cat == 'article') {
                    ctx._source.document.category = 'article';
                } else if (cat == 'book') {
                    ctx._source.document.category = 'book';
                } else if (cat == 'video') {
                    ctx._source.document.category = 'content';
                } else if (cat == 'movie') {
                    ctx._source.document.category = 'movie';
                } else if (cat == 'tv_show') {
                    ctx._source.document.category = 'series';
                } else if (cat == 'livestream') {
                    ctx._source.document.category = 'sport';
                } else if (cat == 'music_video') {
                    ctx._source.document.category = 'music';
                } else {
                    ctx._source.document.category = 'content';
                }
            """,
            "lang": "painless",
        },
        "query": query_to_migrate,
    }
    await update_by_query_async(client=client, data_type=DataType.EventTemplate, query=body)
    final_content_count = (await client.count(index=index_name, body={"query": query_content}))["count"]
    assert final_content_count == origin_content_count + count_to_migrate
    query = Query(
        type_queries=[
            TypeQuery(domain_types=[EventTemplate], query=ValuesQuery(field_name="document_type", values=["content"]))
        ]
    )
    await template_repo.search_by_query(query=query, size=10000)
    await client.close()


if __name__ == "__main__":
    asyncio.run(run_migration(dry_run=True))
