import asyncio
import logging
from typing import Any, Dict

from opensearchpy import As<PERSON><PERSON><PERSON><PERSON>earch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.infrastructure.database.opensearch.index_settings.usage_statistics_output import (
    UsageStatisticsIndexModel,
)
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    OpenSearchIndex,
)

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


def create_migration_script() -> Dict[str, Any]:
    """
    AI generated. Cuz I can.
    Create the migration script to transform usage statistics documents from old to new format.

    Old format:
    - users_with_events: integer (original diary events)
    - users_with_notes: integer
    - users_with_rating: integer

    New format:
    - users_with_events: object with properties for each EventV3Type as integers
    - users_with_notes and users_with_rating fields are removed

    Mapping strategy:
    - Original diary events (users_with_events) -> Activity type
    - Users with ratings -> Emotion type
    - Users with notes -> Note type
    """
    return {
        "source": """
            Map newUsersWithEvents = new HashMap();

            def oldUsersWithEvents = ctx._source.usage_statistics_results?.users_with_events;
            def oldUsersWithNotes = ctx._source.usage_statistics_results?.users_with_notes;
            def oldUsersWithRating = ctx._source.usage_statistics_results?.users_with_rating;

            for (String eventType : params.event_types) {
                newUsersWithEvents.put(eventType, 0);
            }

            if (oldUsersWithEvents != null && oldUsersWithEvents instanceof Number) {
                newUsersWithEvents.put('activity', oldUsersWithEvents);
            }

            if (oldUsersWithRating != null && oldUsersWithRating instanceof Number) {
                newUsersWithEvents.put('emotion', oldUsersWithRating);
            }

            if (oldUsersWithNotes != null && oldUsersWithNotes instanceof Number) {
                newUsersWithEvents.put('note', oldUsersWithNotes);
            }

            ctx._source.usage_statistics_results.users_with_events = newUsersWithEvents;

            ctx._source.usage_statistics_results.remove('users_with_notes');
            ctx._source.usage_statistics_results.remove('users_with_rating');
        """,
        "params": {"event_types": [event_type.value for event_type in EventV3Type]},
    }


async def reindex_usage_stats_documents(client: AsyncOpenSearch, index_model: OpenSearchIndex, dry_run: bool):
    index_name = index_model.name
    count = (await client.count(index=index_name))["count"]
    migration_script = create_migration_script()

    logger.info(f"Reindexing {count} documents from {index_name} to migrate usage statistics from old to new format")

    if dry_run:
        dummy_index = f"dummy-{index_model.name}"
        await OSMigrationWrapper._re_create_index_with_mappings(
            index_model=index_model,
            target_index_name=dummy_index,
            client=client,
        )
        await OSMigrationWrapper.reindex_index_to(
            source=index_name,
            destination=dummy_index,
            client=client,
            script=migration_script,
            pipeline=None,
            query=None,
        )

        new_count = (await client.count(index=dummy_index))["count"]

        logger.info(
            f"[DRY RUN] reindexed {new_count} documents from {index_name} to {dummy_index}\n"
            f"-------------\n"
            f"COUNT_DIFF: {count - new_count}"
        )

        result = await client.indices.delete(index=dummy_index)
        logger.info(f"[DRY RUN] removed index {dummy_index} with result: {result}")
    else:
        await OSMigrationWrapper.reindex_index_through_dummy_index(
            index_name=index_name,
            index_model=index_model,
            client=client,
            script=migration_script,
            pipeline=None,
        )

        new_count = (await client.count(index=index_name))["count"]
        logger.info(f"reindexed {new_count} documents\n" f"-------------\n" f"COUNT_DIFF: {count - new_count}")


async def migrate(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    try:
        await reindex_usage_stats_documents(client=client, index_model=UsageStatisticsIndexModel, dry_run=dry_run)
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(migrate(dry_run=True))
