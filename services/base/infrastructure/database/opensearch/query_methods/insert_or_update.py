import asyncio
import logging
from typing import Op<PERSON>, Sequence

from opensearchpy import <PERSON><PERSON><PERSON><PERSON><PERSON>earch, OpenSearch, OpenSearchException

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.shared import BaseDataModel
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_OS_TASK_COMPLETED
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataTypeToIndexModelMapping,
    data_type_to_index_mapping_get,
)
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils


async def snapshot_in_progress(client: AsyncOpenSearch) -> bool:
    resp = await client.snapshot.status()
    # TODO: granual check for a specific index in the snapshot
    # The response contains "{'snapshots': []}" if no snapshot is running
    return bool(resp.get("snapshots"))


async def await_no_running_snapshot(client: AsyncOpenSearch):
    # Check if there are any running snapshots of indices
    while await snapshot_in_progress(client):
        # There is a running snapshot
        logging.info("waiting for a snapshot to finish")
        await asyncio.sleep(5)


async def await_tasks_completed(client: AsyncOpenSearch, task_ids: Sequence[str]):
    tasks_iterable = task_ids
    while tasks_iterable:
        tasks_pending: list[str] = []
        for task_id in tasks_iterable:
            try:
                task: dict | None = await client.tasks.get(task_id=task_id)
            except OpenSearchException as error:
                logging.error(f"Error processing opensearch task {task_id}, err: {error}")
                task = None
            if task:
                if not task.get(OS_LABEL_OS_TASK_COMPLETED):
                    tasks_pending.append(task_id)
        if not tasks_pending:
            await asyncio.sleep(1)
        tasks_iterable = tasks_pending


async def update_by_query_async(
    data_type: DataType,
    query: dict,
    client: AsyncOpenSearch,
    requests_per_second: Optional[int] = None,
    pipeline: Optional[str] = None,
    request_timeout: int = 3600,
):
    await await_no_running_snapshot(client=client)
    logging.info(f"running update by query {query} for data_type {data_type}.")
    index_pattern = DataTypeToIndexModelMapping[data_type].name
    await update_by_query_by_index_pattern(
        index_pattern=index_pattern,
        query=query,
        client=client,
        requests_per_second=requests_per_second,
        pipeline=pipeline,
        request_timeout=request_timeout,
    )
    logging.info(f"completed update by query {query} for data_type {data_type}.")


async def update_by_query_by_index_pattern(
    index_pattern: str,
    query: dict,
    client: AsyncOpenSearch,
    requests_per_second: Optional[int] = None,
    pipeline: Optional[str] = None,
    request_timeout: int = 3600,
):
    batch_size = 10
    indices = await OpenSearchUtils.get_index_names_async(index_pattern=index_pattern)
    batches = [indices[i : i + batch_size] for i in range(0, len(indices), batch_size)]
    for batch in batches:
        task_ids = []
        for index_name in batch:
            response = await client.update_by_query(
                index=index_name,
                body=query,
                request_timeout=request_timeout,  # pyright: ignore
                conflicts="proceed",  # pyright: ignore
                slices="auto",  # pyright: ignore
                refresh=True,  # pyright: ignore
                error_trace=True,  # pyright: ignore
                requests_per_second=requests_per_second,  # pyright: ignore
                pipeline=pipeline,  # pyright: ignore
                wait_for_completion=False,  # pyright: ignore
            )
            task_id = response["task"]
            task_ids.append(task_id)
            logging.info(f"running update by query for index: {index_name} (task_id: {task_id})")
        await await_tasks_completed(client=client, task_ids=task_ids)


async def reindex(
    query: dict,
    client: AsyncOpenSearch,
    requests_per_second: Optional[int] = None,
    request_timeout: int = 3600,
):
    await await_no_running_snapshot(client=client)
    task_id = (
        await client.reindex(
            body=query,
            request_timeout=request_timeout,  # pyright: ignore
            slices="auto",  # pyright: ignore
            refresh=True,  # pyright: ignore
            error_trace=True,  # pyright: ignore
            requests_per_second=requests_per_second,  # pyright: ignore
            wait_for_completion=False,  # pyright: ignore
        )
    )["task"]
    logging.info(f"running reindex query: {query} \nReindex task id: {task_id}")
    await await_tasks_completed(client=client, task_ids=[task_id])
    logging.info(f"completed reindex query {query} \nReindex task id: {task_id}")


async def index_async(
    client: AsyncOpenSearch, data_type: DataType, document: BaseDataModel, doc_id: Optional[str] = None
):
    return await client.index(
        index=data_type_to_index_mapping_get(data_type), body=document.model_dump_json(by_alias=True), id=doc_id
    )


async def try_index_async(data_type: DataType, document: BaseDataModel, client: AsyncOpenSearch):
    try:
        await index_async(data_type=data_type, document=document, client=client)
    except Exception as error:
        logging.exception(f"Error: \n{error} \nindexing document: \n{document}.")


def index(client: OpenSearch, data_type: DataType, document: BaseDataModel, doc_id: Optional[str] = None):
    return client.index(
        index=data_type_to_index_mapping_get(data_type), body=document.model_dump_json(by_alias=True), id=doc_id
    )
