from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content import (
    ContentCategory,
    ContentFields,
    ContentIdentifier,
    ContentValueLimits,
)
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


class ContentTemplatePayload(TemplatePayloadBase, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY)
    title: NonEmptyStr | None = Field(alias=ContentFields.TITLE, max_length=ContentValueLimits.TITLE_MAX_LENGTH)
    url: NonEmptyStr | None = Field(alias=ContentFields.URL, max_length=ContentValueLimits.URL_MAX_LENGTH)
    rating: int | None = Field(
        alias=ContentFields.RATING,
        ge=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
        le=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
    )
