from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class ContentCategory(StrEnum):
    CONTENT = DataType.Content
    MOVIE = auto()
    SERIES = auto()
    SPORT = auto()
    GAME = auto()
    SOCIAL = auto()
    MUSIC = auto()
    PODCAST = auto()
    AUDIOBOOK = auto()
    BOOK = auto()
    ARTICLE = auto()
    NEWS = auto()
    SHORT = auto()


@dataclass(frozen=True)
class ContentValueLimits:
    CONTENT_RATING_MINIMUM_VALUE = 0
    CONTENT_RATING_MAXIMUM_VALUE = 10
    TITLE_MAX_LENGTH = 256
    URL_MAX_LENGTH = 2056


@dataclass(frozen=True)
class ContentFields(EventFields):
    TITLE = "title"
    URL = "url"
    RATING = "rating"


class ContentIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Content


class Content(Event, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY, default=ContentCategory.CONTENT)
    title: NonEmptyStr | None = Field(alias=ContentFields.TITLE, max_length=ContentValueLimits.URL_MAX_LENGTH)
    url: str | None = Field(alias=ContentFields.URL, max_length=ContentValueLimits.URL_MAX_LENGTH)
    rating: int | None = Field(
        alias=ContentFields.RATING,
        ge=ContentValueLimits.CONTENT_RATING_MINIMUM_VALUE,
        le=ContentValueLimits.CONTENT_RATING_MAXIMUM_VALUE,
    )
