from __future__ import annotations

from enum import StrEnum

from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.shared_v2 import DeprEventModel


class EventType(StrEnum):
    # V3
    # Content Collection
    Content = DataType.Content

    # Body Metric Collection
    BloodGlucose = DataType.BloodGlucose
    BloodPressure = DataType.BloodPressure
    BodyMetric = DataType.BodyMetric
    # Exercise Collection
    Cardio = DataType.Cardio
    Exercise = DataType.Exercise
    Strength = DataType.Strength

    # Feelings Collection
    Emotion = DataType.Emotion
    Stress = DataType.Stress

    # Nutrition Collection
    Drink = DataType.Drink
    Food = DataType.Food
    Supplement = DataType.Supplement

    # Core
    Activity = DataType.Activity
    SleepV3 = DataType.SleepV3
    EventGroup = DataType.EventGroup
    Note = DataType.Note
    Symptom = DataType.Symptom
    Medication = DataType.Medication
    Person = DataType.Person
    PlaceVisit = DataType.PlaceVisit

    # V2
    HeartRate = DataType.HeartRate
    RestingHeartRate = DataType.RestingHeartRate
    Sleep = DataType.Sleep
    Steps = DataType.Steps
    Location = DataType.Location

    def to_domain_model(self) -> type[Event | DeprEventModel]:
        from services.base.type_resolver import TypeResolver

        try:
            return TypeResolver.get_event(type_id=self)
        except ShouldNotReachHereException:
            try:
                return TypeResolver.get_event_v2(type_id=self)
            except ShouldNotReachHereException:
                raise ShouldNotReachHereException(f"Unable to convert Event type {self.value} to domain model")
