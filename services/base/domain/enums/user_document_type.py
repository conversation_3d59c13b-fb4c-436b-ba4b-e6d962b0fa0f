from enum import StrEnum

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document
from services.base.type_resolver import TypeResolver


class UserDocumentType(StrEnum):
    # Body Metric Collection
    BloodGlucose = DataType.BloodGlucose
    BloodPressure = DataType.BloodPressure
    BodyMetric = DataType.BodyMetric

    # Content Collection
    Content = DataType.Content

    # Exercise Collection
    Cardio = DataType.Cardio
    Exercise = DataType.Exercise
    Strength = DataType.Strength

    # Feeling Collection
    Emotion = DataType.Emotion
    Stress = DataType.Stress

    # Nutrition Collection
    Drink = DataType.Drink
    Food = DataType.Food
    Supplement = DataType.Supplement

    # Other Events
    Activity = DataType.Activity
    SleepV3 = DataType.SleepV3
    Note = DataType.Note
    Symptom = DataType.Symptom
    Medication = DataType.Medication
    Person = DataType.Person
    PlaceVisit = DataType.PlaceVisit

    # Records
    SleepRecord = DataType.SleepRecord
    BodyMetricRecord = DataType.BodyMetricRecord
    StepsRecord = DataType.StepsRecord

    # Documents
    EventTemplate = DataType.EventTemplate
    GroupTemplate = DataType.GroupTemplate
    Plan = DataType.Plan
    UseCase = DataType.UseCase
    EventGroup = DataType.EventGroup
    Contact = DataType.Contact
    Place = DataType.Place

    HeartRate = DataType.HeartRate
    RestingHeartRate = DataType.RestingHeartRate
    Sleep = DataType.Sleep
    Steps = DataType.Steps
    Location = DataType.Location
    ShoppingActivity = DataType.ShoppingActivity

    ExtensionRun = DataType.ExtensionRun
    ExtensionResult = DataType.ExtensionResult
    InboxMessage = DataType.InboxMessage
    # TODO:
    # UserLogs = DataType.UserLogs

    def to_domain_model(self) -> type[Document]:
        return TypeResolver.get_document(self)
