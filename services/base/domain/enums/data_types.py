from __future__ import annotations

from enum import StrEnum
from typing import Type

from services.base.domain.schemas.events.document_base import Document


class DataType(StrEnum):
    # Content
    Content = "content"

    # Feeling
    Stress = "stress"
    Emotion = "emotion"

    # Exercise
    Exercise = "exercise"
    Cardio = "cardio"
    Strength = "strength"

    # Body Metric
    BloodGlucose = "blood_glucose"
    BloodPressure = "blood_pressure"
    BodyMetric = "body_metric"

    # Medication
    Medication = "medication"
    SleepV3 = "sleep"

    # Other
    Person = "person"
    PlaceVisit = "place_visit"
    Activity = "activity"
    Note = "note"
    Symptom = "symptom"
    EventGroup = "event_group"

    # Nutrition
    Drink = "drink"
    Food = "food"
    Supplement = "supplement"

    # Records
    SleepRecord = "sleep_record"
    StepsRecord = "steps_record"
    BodyMetricRecord = "body_metric_record"

    # Documents
    EventTemplate = "event_template"
    GroupTemplate = "group_template"
    UseCase = "use_case"
    Contact = "contact"
    Place = "place"

    # Scheduled
    Plan = "plan"
    Goal = "goal"

    # V2
    HeartRate = "HeartRate"
    Location = "Location"
    RestingHeartRate = "RestingHeartRate"
    ShoppingActivity = "ShoppingActivity"
    Sleep = "Sleep"
    Steps = "Steps"

    # Environment
    AirQuality = "AirQuality"
    Weather = "Weather"
    Pollen = "Pollen"

    # Other
    ExtensionRun = "extension_run"
    ExtensionResult = "extension_result"
    InboxMessage = "InboxMessage"
    UserLogs = "UserLogs"
    UsageStatistics = "UsageStatistics"

    def to_domain_model(self) -> Type[Document]:
        from services.base.type_resolver import TypeResolver

        return TypeResolver.get_document(self)
