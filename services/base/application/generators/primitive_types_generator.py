import os
import random
import string
import struct
from datetime import date, datetime, timedelta, timezone
from typing import Any, List, Optional, Type
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo

from services.base.application.utils.time import tz_infos
from services.base.domain.annotated_types import SerializableAwareDatetime, UniqueSequenceStr
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel
from services.base.infrastructure.database.opensearch.doc_id_refiner import DocIdRefiner
from services.base.infrastructure.database.opensearch.opensearch_index_constants import ALIAS_FIRST_INDEX_POINTER
from services.base.infrastructure.database.opensearch.opensearch_mappings import DataTypeToIndexModelMapping


def allow_none(func):
    """Decorator that adds a 50% chance of returning None based on the allow_none parameter."""

    def wrapper(*args, **kwargs) -> Optional[Any]:
        allow_none = kwargs.get("allow_none", False)
        if allow_none and random.choice([True, False]):
            return None
        return func(*args, **kwargs)

    return wrapper


class PrimitiveTypesGenerator:
    @staticmethod
    @allow_none
    def generate_random_pdf_bytes(allow_none: bool = False) -> bytes:
        header = b"%PDF-1.4\n"
        body = PrimitiveTypesGenerator.generate_random_bytes()
        footer = b"%%EOF\n"
        content = header + body + footer
        return content

    @staticmethod
    @allow_none
    def generate_random_image_bytes(allow_none: bool = False) -> bytes:
        # WebP header: RIFF + file size + WEBP signature
        riff_header = b"RIFF"
        webp_signature = b"WEBP"

        # Fake image data
        random_image_data = os.urandom(10)  # Random data for the example

        # VP8 chunk header for lossy WebP
        vp8_header = b"VP8 "
        vp8_chunk_size = struct.pack("<I", len(random_image_data))

        # RIFF chunk size: 4 (signature) + 4 (VP8 header) + size of the VP8 data
        riff_chunk_size = struct.pack("<I", 4 + len(vp8_header) + len(random_image_data))

        # Concatenate all parts to form the WebP file
        webp_data = riff_header + riff_chunk_size + webp_signature + vp8_header + vp8_chunk_size + random_image_data

        return webp_data

    @staticmethod
    @allow_none
    def generate_random_audio_bytes(allow_none: bool = False) -> bytes:
        header = b"RIFF"
        wave = b"WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x44\xac\x00\x00\x88\x58\x01\x00\x02\x00\x10\x00"
        data = (
            b"data"
            + struct.pack("<I", PrimitiveTypesGenerator.generate_random_int())
            + b"\x00" * PrimitiveTypesGenerator.generate_random_int()
        )
        return header + struct.pack("<I", len(data) + 36) + wave + data

    @staticmethod
    @allow_none
    def generate_random_video_bytes(allow_none: bool = False) -> bytes:
        ftyp = b"\x00\x00\x00\x18ftypmp42\x00\x00\x00\x00mp42isom"
        mdat = b"\x00\x00\x00\x08mdat" + PrimitiveTypesGenerator.generate_random_bytes()
        moov = b"\x00\x00\x00\x6cmoov\x00\x00\x00\x6cmvhd"

        content = ftyp + mdat + moov
        return content

    @staticmethod
    @allow_none
    def generate_random_string(allow_none: bool = False, min_length: int = 1, max_length: int = 10) -> str:
        return "".join(random.choice(string.ascii_letters) for _ in range(random.randint(min_length, max_length)))

    @staticmethod
    @allow_none
    def generate_n_random_strings(allow_none: bool = False, n: Optional[int] = None) -> List[str]:
        size = n if n else PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5)
        return [PrimitiveTypesGenerator.generate_random_string() for _ in range(size)]

    @staticmethod
    @allow_none
    def generate_random_bytes(allow_none: bool = False, min_length: int = 1, max_length: int = 100) -> bytes:
        return b"".join(
            random.choice(string.ascii_letters).encode() for _ in range(random.randint(min_length, max_length))
        )

    @staticmethod
    @allow_none
    def generate_random_bool(allow_none: bool = False) -> bool:
        return random.choice([True, False])

    @staticmethod
    def generate_random_tags() -> UniqueSequenceStr:
        return list(
            set(
                [
                    PrimitiveTypesGenerator.generate_random_string()
                    for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=3))
                ]
            )
        )

    @staticmethod
    @allow_none
    def generate_random_rrule(started_at: datetime | None, allow_none: bool = False) -> CustomRRule | None:
        freq = random.choice(("YEARLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "MINUTELY"))
        interval = 1 if freq != "MINUTELY" else random.randint(60, 120)
        rule = CustomRRule.from_rrule(
            f"DTSTART:{(started_at or PrimitiveTypesGenerator.generate_random_aware_datetime()).isoformat()}\nRRULE:FREQ={freq};INTERVAL={interval}"
        )
        return rule

    @staticmethod
    @allow_none
    def generate_random_aware_datetime(
        lte: datetime | None = None,
        gte: datetime | None = None,
        allow_none: bool = False,
    ) -> SerializableAwareDatetime:
        now = datetime.now(timezone.utc)
        if lte and gte:
            dt_max = lte
            dt_min = gte
        elif lte and not gte:
            dt_max = lte
            dt_min = dt_max - timedelta(days=365)
        elif gte and not lte:
            dt_min = gte
            dt_max = dt_min + timedelta(days=365)
        else:
            dt_max = now
            dt_min = dt_max - timedelta(days=365)
        return datetime.fromtimestamp(
            timestamp=random.randint(int(dt_min.timestamp()), int(dt_max.timestamp())), tz=ZoneInfo("UTC")
        )

    @staticmethod
    @allow_none
    def generate_random_date(
        min_date: date | None = None,
        max_date: date | None = None,
        allow_none: bool = False,
    ) -> date:
        min_datetime = None
        max_datetime = None

        if min_date:
            min_datetime = datetime.combine(min_date, datetime.min.time(), timezone.utc)
        if max_date:
            max_datetime = datetime.combine(max_date, datetime.max.time(), timezone.utc)

        random_datetime = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=min_datetime, lte=max_datetime)

        return random_datetime.date()

    @staticmethod
    @allow_none
    def generate_random_timedelta(
        min_timedelta: timedelta | None = None, max_timedelta: timedelta | None = None, allow_none: bool = False
    ) -> timedelta:
        max_seconds = int(max_timedelta.total_seconds()) if max_timedelta else SECONDS_IN_365_DAYS
        min_seconds = int(min_timedelta.total_seconds()) if min_timedelta else 0

        random_seconds = random.randint(min_seconds, max_seconds)

        return timedelta(seconds=random_seconds)

    @staticmethod
    @allow_none
    def generate_random_enum(
        enum_type,
        allow_none: bool = False,
    ):
        return random.choice(list(enum_type))

    @staticmethod
    @allow_none
    def generate_random_int(min_value: int = 0, max_value: int = 10_000, allow_none: bool = False) -> int:
        return random.randint(min_value, max_value)

    @staticmethod
    @allow_none
    def generate_random_zoneinfo(allow_none: bool = False) -> ZoneInfo:
        return random.choice(list(tz_infos.values()))

    @staticmethod
    @allow_none
    def generate_random_float(
        min_value: float = 0,
        max_value: float = 10_000,
        decimal_numbers: Optional[int] = None,
        allow_none: bool = False,
    ) -> float:
        if decimal_numbers:
            return round(random.uniform(min_value, max_value), decimal_numbers)
        return random.uniform(min_value, max_value)

    @staticmethod
    @allow_none
    def generate_random_timezone(allow_none: bool = False) -> ZoneInfo:
        return random.choice(list(tz_infos.values()))

    @staticmethod
    @allow_none
    def get_random_field_name[T: BaseDataModel](domain_model_class: Type[T], allow_none: bool = False) -> str:
        fields = list(domain_model_class.model_fields)
        return random.choice(fields)

    @staticmethod
    @allow_none
    def get_random_coordinates(allow_none: bool = False) -> CoordinatesModel:
        latitude = PrimitiveTypesGenerator.generate_random_float(min_value=-90, max_value=90)
        longitude = PrimitiveTypesGenerator.generate_random_float(min_value=-180, max_value=180)
        return CoordinatesModel(lat=latitude, lon=longitude)

    @staticmethod
    @allow_none
    def generate_https_url(allow_none: bool = False) -> str:
        return (
            "https://"
            + PrimitiveTypesGenerator.generate_random_string()
            + "."
            + random.choice([".com", ".ai", ".io", ".co", ".cz"])
        )

    @staticmethod
    def get_random_type_id() -> str:
        from services.base.type_resolver import TypeResolver

        event = random.choice(TypeResolver.EVENTS_V3)
        return event.type_id()

    @staticmethod
    def generate_random_uuid() -> UUID:
        return uuid4()

    @staticmethod
    def generate_random_typed_uuid(data_type: DataType) -> UUID:
        # Assumes tests never reach more than the first index pointer
        index_name = f"{DataTypeToIndexModelMapping[data_type].name}-{ALIAS_FIRST_INDEX_POINTER}"
        return DocIdRefiner.refine_doc_id(index_name=index_name, doc_id=uuid4())
