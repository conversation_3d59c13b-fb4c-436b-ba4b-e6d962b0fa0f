from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.schemas.events.content.content import ContentIdentifier
from services.base.domain.schemas.templates.payload.content_template_payloads import (
    ContentTemplatePayload,
)
from services.base.tests.domain.builders.content.content_builder import ContentBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class ContentPayloadBuilder(EventPayloadBuilderBase, ContentIdentifier):

    def build(self) -> ContentTemplatePayload:
        return ContentTemplatePayload.map(
            model=ContentBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
