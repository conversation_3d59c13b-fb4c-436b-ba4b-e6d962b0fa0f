from services.base.domain.schemas.events.content.content import ContentFields
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.csv_mappings.v3_documents.v3_non_collection_mappings import (
    V3CSVEventBaseMapping,
)

ContentCollectionCSVMappings = {
    ExportableType.Content: [
        *V3CSVEventBaseMapping,
        # Content Fields
        ContentFields.TITLE,
        ContentFields.URL,
        ContentFields.RATING,
        ContentFields.NOTE,
    ],
}
