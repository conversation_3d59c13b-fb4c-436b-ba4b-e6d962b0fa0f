# -*- coding: utf-8 -*-
import json
import logging
import os
from typing import Any, Dict
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.enums.provider import Provider
from services.base.domain.enums.units.weight import WeightUnits
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric, BodyMetricCategory
from services.base.domain.schemas.events.document_base import EventMetadata, RBACSchema
from services.base.infrastructure.database.opensearch.doc_id_refiner import DocIdRefiner
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    BODY_METRIC_INDEX,
)
from services.file_service.application.converters import partial_timestamp_parser
from services.file_service.application.file_handlers import find_file_paths_matching_regex_format
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.application.loaders.fitbit_loaders.fitbit_constants import FITBIT_PERSONAL, FITBIT_ROOT_PATH
from services.file_service.application.loaders.fitbit_loaders.utils import get_person_folder, get_units
from settings.app_constants import MESSAGE_NO_FILES_FOUND, MESSAGE_UNABLE_TO_LOAD

fitbit_weight_to_kilogram_unit_conversion = {
    WeightUnits.STONES: 0.**********,
    WeightUnits.POUNDS: 2.20462,
    WeightUnits.KILOGRAMS: 1,
}


class WeightLoader(FileLoaderBase):
    """Loader for Fit Bit export file"""

    _source = "weight_and_body_fat"
    PROVIDER = Provider.FITBIT

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_paths = []
        fitbit_personal_path = os.path.join(
            get_person_folder(os.path.join(data_dir_path, FITBIT_ROOT_PATH)), FITBIT_PERSONAL
        )
        self.search_path = os.path.join(data_dir_path, fitbit_personal_path)
        self.unit_model = get_units(profile_file_path=os.path.join(fitbit_personal_path, "Profile.csv"))

    def load_files_data(self) -> None:
        # Example weight file: weight-2018-03-24.json
        regex_format = r"^weight.*json$"
        self.file_paths = list(find_file_paths_matching_regex_format(self.search_path, regex_format))
        if not self.file_paths:
            logging.exception("%s! Folder: %s", MESSAGE_NO_FILES_FOUND, self.search_path)

        return self.process_data()

    def process_data(self) -> None:
        for file_path in self.file_paths:
            self._process_weight_files(file_path)

    def _parse_data_to_entry(self, entry: Dict[str, Any]) -> tuple[BodyMetric | None, BodyMetric | None]:
        timestamp = entry.get(DocumentLabels.TIMESTAMP)
        metadata = EventMetadata(
            origin=Origin.FITBIT,
            service=Service.FILE,
            origin_device=None,
            source_service=SourceService.FITBIT,
            source_os=SourceOS.UNKNOWN,
            organization=Organization.FITBIT,
        )
        rbac = RBACSchema(owner_id=self.user_uuid)

        weight = entry.get("weight")
        weight = weight * fitbit_weight_to_kilogram_unit_conversion[self.unit_model.weight] if weight else None
        body_fat = entry.get("fat")
        s_id = uuid4()

        return (
            (
                BodyMetric(
                    id=DocIdRefiner.refine_doc_id(
                        doc_id=uuid4(), index_name=f"{BODY_METRIC_INDEX}-{ALIAS_FIRST_INDEX_POINTER}"
                    ),
                    timestamp=timestamp,
                    value=weight,
                    metadata=metadata,
                    rbac=rbac,
                    name="weight",
                    submission_id=s_id,
                    template_id=None,
                    note=None,
                    asset_references=[],
                    type=DataType.BodyMetric,
                    category=BodyMetricCategory.WEIGHT,
                    tags=[],
                    plan_extension=None,
                    group_id=None,
                    end_time=None,
                )
                if weight
                else None
            ),
            (
                BodyMetric(
                    id=DocIdRefiner.refine_doc_id(
                        doc_id=uuid4(), index_name=f"{BODY_METRIC_INDEX}-{ALIAS_FIRST_INDEX_POINTER}"
                    ),
                    timestamp=timestamp,
                    value=body_fat,
                    metadata=metadata,
                    rbac=rbac,
                    name="body fat",
                    submission_id=s_id,
                    template_id=None,
                    note=None,
                    asset_references=[],
                    type=DataType.BodyMetric,
                    category=BodyMetricCategory.BODY_FAT,
                    tags=[],
                    plan_extension=None,
                    group_id=None,
                    end_time=None,
                )
                if body_fat
                else None
            ),
        )

    def _process_weight_files(self, file_path: str) -> None:
        entries = []
        try:
            with open(file_path, encoding="utf8") as json_file:
                data_list = json.load(json_file)
                for data in data_list:
                    data[DocumentLabels.TIMESTAMP] = self._parse_tz_datetime(
                        partial_timestamp_parser(data.get("date") + " " + data.get("time"))
                    )
                    weight, body_fat = self._parse_data_to_entry(data)
                    if weight:
                        entries.append(weight)
                    if body_fat:
                        entries.append(body_fat)
                    self._commit_if_limit_reached(entries)

            self._commit(entries=entries)

        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, file_path)
