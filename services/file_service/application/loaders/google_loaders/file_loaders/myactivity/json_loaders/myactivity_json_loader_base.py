import logging
import os
from abc import abstractmethod
from typing import Any, Dict, Optional, Tuple
from uuid import UUID, uuid4
from zoneinfo import ZoneInfo

import ijson
from opensearchpy import OpenSearch

from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.events.content.content import Content, ContentCategory
from services.base.domain.schemas.events.document_base import EventMetadata, RBACSchema
from services.base.infrastructure.database.opensearch.doc_id_refiner import DocIdRefiner
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    ALIAS_FIRST_INDEX_POINTER,
    CONTENT_INDEX,
)
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from settings.app_constants import MESSAGE_UNABLE_TO_LOAD


class InvalidEntryException(Exception):
    pass


class SkipEntryException(Exception):
    pass


class MyActivityJsonLoaderBase(FileLoaderBase):
    """Base loader for my activity JSON files"""

    KEY_TIME = "time"
    KEY_HEADER = "header"
    KEY_ACTION_TYPE = "actionType"
    KEY_TITLE = "title"
    KEY_TITLE_URL = "titleUrl"
    KEY_SUBTITLES = "subtitles"
    KEY_PRODUCTS = "products"
    KEY_LOCATION_INFOS = "locationInfos"
    KEY_NAME = "name"
    KEY_URL = "url"
    KEY_SOURCE = "source"
    KEY_SOURCE_URL = "sourceUrl"

    PROVIDER = Provider.GOOGLE
    URL_PREFIX = "https://www.google.com/url?q="
    DEFAULT_ACTION = None

    COUNT = 0
    INVALID_COUNT = 0
    ERROR_COUNT = 0
    SKIPPED_COUNT = 0
    CONTENT_CATEGORY_TO_ACTION_MAP: Dict[ContentCategory, Tuple[str, ...]] = {
        # Video content
        ContentCategory.CONTENT: ("watched a video in", "saw videos in", "watched"),
        ContentCategory.MOVIE: ("watched movie",),
        ContentCategory.SERIES: ("watched tv show", "watched episode"),
        # Audio content
        ContentCategory.MUSIC: ("played song", "played music", "listened to", "listened"),
        ContentCategory.PODCAST: ("played podcast",),
        # Text content
        ContentCategory.ARTICLE: (
            "saw articles in",
            "read article",
            "read article",
            "read",
            "read news",
            "viewed news",
        ),
        # Interactive content
        ContentCategory.GAME: ("played game", "played"),
    }

    ACTIONS_TO_SKIP = (
        (
            "clicked",
            "dismissed notification about",
            "closed",
            "dismissed",
            "navigated",
            "initiated",
            "installed",
            "requested",
            "downloaded",
            "managed",
            "received notification about",
            "received",
            "used google",
        ),
    )

    @abstractmethod
    def __init__(
        self,
        user_uuid: UUID,
        data_type: DataType,
        data_dir_path: str,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)
        self.file_path = ""
        self.search_path = data_dir_path

    def _parse_to_content(self, user_uuid: UUID, data_entry: Dict[str, Any]) -> Content:
        """Parses data into content detail entity"""
        category, title = self._get_category_title(data_entry[self.KEY_TITLE])
        entity, title_url = self._get_entity_and_url(data_entry)

        if not all([title_url, entity, category, title]):
            raise InvalidEntryException

        metadata = EventMetadata(
            origin=Origin.GOOGLE,
            organization=Organization.GOOGLE,
            service=Service.FILE,
            source_service=SourceService.TAKEOUT,
            origin_device=None,
            source_os=SourceOS.UNKNOWN,
        )

        # Use the returned content_class to create the appropriate instance
        return Content(
            id=DocIdRefiner.refine_doc_id(doc_id=uuid4(), index_name=f"{CONTENT_INDEX}-{ALIAS_FIRST_INDEX_POINTER}"),
            type=DataType.Content,
            category=category if category else ContentCategory.CONTENT,
            template_id=None,
            note=None,
            name=title,
            title=title,
            asset_references=[],
            rbac=RBACSchema(owner_id=user_uuid),
            timestamp=self._parse_tz_datetime(data_entry[self.KEY_TIME]),
            url=title_url,
            metadata=metadata,
            submission_id=uuid4(),
            rating=None,
            tags=[entity],
            group_id=None,
            plan_extension=None,
            end_time=None,
        )

    def _get_category_title(self, activity_and_title: str) -> Tuple[ContentCategory, str]:
        """Splits activity and title to separate strings and determines the Content type.

        Returns:
            tuple of (content_category, title, content_type) where:
            - content_category: str - The category of content or None if not found
            - title: str - The extracted title
            - content_type: type[Content] - The appropriate Content class to instantiate
        """
        activity_lower = activity_and_title.lower()
        if self._should_skip_entry(activity_lower):
            raise SkipEntryException
        # Check each content type and its activity patterns
        for content_category, patterns in self.CONTENT_CATEGORY_TO_ACTION_MAP.items():
            for pattern in patterns:
                if activity_lower.startswith(pattern):
                    title = activity_and_title[len(pattern) :].strip()
                    return content_category, title

        logging.info("No content mapping found for activity: %s", activity_and_title)
        raise InvalidEntryException

    @abstractmethod
    def _get_entity_and_url(self, data_entry: Dict[str, Any]) -> Tuple[Optional[str], Optional[str]]:
        """Gets entity name and title url as separate field from corresponding data_entry"""

    def _should_skip_entry(self, activity_and_title: str) -> bool:
        for pattern in self.ACTIONS_TO_SKIP:
            if activity_and_title.startswith(pattern):
                return True
        return False

    def load_files_data(self) -> None:
        self.file_path = os.path.join(self.search_path, "MyActivity.json")
        self.process_data()

    def process_data(self) -> None:
        try:
            json_entries = ijson.items(open(os.path.normpath(self.file_path), encoding="utf8"), "item")
            entries = []
            json_entry: dict
            for json_entry in json_entries:
                try:
                    self.COUNT += 1
                    data_entry = json_entry.copy()
                    content = self._parse_to_content(data_entry=data_entry, user_uuid=self.user_uuid)
                    entries.append(content)
                    self._commit_if_limit_reached(entries)
                except InvalidEntryException:
                    self.INVALID_COUNT += 1
                    logging.warning("Error processing entry: %s", json_entry)
                    continue
                except SkipEntryException:
                    self.SKIPPED_COUNT += 1
                    continue
                except Exception as error:  # pylint:disable=broad-except
                    self.ERROR_COUNT += 1
                    logging.exception("Error processing entry: %s, %s", json_entry, repr(error))
                    continue
            self._commit(entries=entries)
        except FileNotFoundError:
            logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, self.file_path)
        logging.info(
            f"Out of {self.COUNT}, {self.ERROR_COUNT} failed, {self.INVALID_COUNT}"
            + f" was found invalid and {self.SKIPPED_COUNT} was skipped.",
        )
